// "use client";
// import { useState } from 'react';
// import { useRouter } from 'next/navigation';
// import { useForm } from 'react-hook-form';
// import FormWrapper from '../reuseable/FormWrapper';
// import RoundRadioButton from '../reuseable/RoundRadioButton';
// import UploadedRecords from '../reuseable/UploadedRecords';
// import { z } from 'zod';
// import { zodResolver } from '@hookform/resolvers/zod';
// import RoundCheckbox from '../reuseable/RoundCheckbox';
// import IntraOralScan from '../patient-records/IntraOralScan';
// import StlUploads from '../patient-records/StlUploads';
// import Image from 'next/image';



// type UserFormData = z.infer<typeof patientDataSchema>;

// const Scan = () => {
//   const router = useRouter();
//   const { register } = useForm<UserFormData>({ resolver: zodResolver(patientDataSchema), });
//   const [uploadOption, setUploadOption] = useState<string>('intraoral-scans');
//   const [scan, setScan] = useState<string>('');
//   const [cbctScan, setCbctScan] = useState<boolean>(false);
//   const [portrait, setPortrait] = useState<File | null>(null)


//   const onSubmit = () => {
//     console.log("Valid form data:", { uploadOption: uploadOption, scan, cbctScan });
//     router.push("/photos");
//   };


//   return (
//     <>
//       <FormWrapper classNames='!grid-cols-1' onSubmit={() => onSubmit()} onBack={() => router.back()}>
//         <div className='col-span-1 flex flex-col flex-grow'>
//           <div className='flex xl:gap-12 gap-4 flex-wrap mb-5'>
//             <RoundRadioButton
//               onClick={(e: React.MouseEvent<HTMLInputElement>) => setUploadOption((e.target as HTMLInputElement).value)}
//               id="stl-uploads"
//               label="STL uploads"
//               value="stl-uploads"
//               name="uploadOption"
//               register={register}
//               defaultChecked={uploadOption === 'stl-uploads'}
//               labelClass='!text-dark font-medium'
//             />
//             <RoundRadioButton
//               onClick={(e: React.MouseEvent<HTMLInputElement>) => setUploadOption((e.target as HTMLInputElement).value)}
//               id="intraoral-scans"
//               label="Intraoral scans"
//               value="intraoral-scans"
//               name="uploadOption"
//               register={register}
//               defaultChecked={uploadOption === 'intraoral-scans'}
//               labelClass='!text-dark font-medium'
//             />
//             <RoundRadioButton
//               onClick={(e: React.MouseEvent<HTMLInputElement>) => setUploadOption((e.target as HTMLInputElement).value)}
//               id="pvs-impressions"
//               label="PVS impressions"
//               value="pvs-impressions"
//               name="uploadOption"
//               register={register}
//               defaultChecked={uploadOption === 'pvs-impressions'}
//               labelClass='!text-dark font-medium'

//             />
//             <RoundRadioButton
//               onClick={(e: React.MouseEvent<HTMLInputElement>) => setUploadOption((e.target as HTMLInputElement).value)}
//               id="decide-later"
//               label="Decide later"
//               value="later"
//               name="uploadOption"
//               register={register}
//               defaultChecked={uploadOption === 'later'}
//               labelClass='!text-dark font-medium'
//             />
//           </div>

//           <div className='grid !grid-cols-12 !gap-6 flex-grow'>

//             <div className="xl:col-span-8 col-span-12 gap-4 !bg-white rounded-[10px]">

//               {uploadOption == "intraoral-scans" && <IntraOralScan register={register} scan={scan} setScan={setScan} />}
//               {uploadOption == "stl-uploads" && <StlUploads  setPortrait={setPortrait}/>}

//               <div className='w-full border-t border-gray mb-7'>
//               </div>

//               <div className='flex flex-col gap-4'>
//                 <div>
//                   <RoundCheckbox
//                     onChange={(e: React.ChangeEvent<HTMLInputElement>) => setCbctScan(e.target.checked ? true : false)}
//                     id="cbctScan"
//                     label="CBCT Scan"
//                     sup='?'
//                     value=""
//                     name="uploadOption"
//                     labelClass='!text-dark font-bold !text-xl'
//                   />
//                 </div>
//                 <div className=''>
//                   <p className=' text-dark mb-1 font-medium'>Select if you want this case to have CBCT feature.</p>
//                   <p className=' text-gray font-medium'>For doctors submitting Traditional Rx from along with CBCT scans, there are some temporary limitations:</p>
//                   <ul className='list-disc text-gray ps-7'>
//                       <li>Only available with Additional Aligners, Comprehensive, Phase 2 Comprehensive.
//                       </li>
//                       <li>Mandibular Advancement Feature is not supported.</li>
//                     </ul>
//                 </div>
//               </div>

//             </div>


//             <div className="xl:col-span-4 col-span-12 flex flex-col gap-4">
//               <UploadedRecords withScan={true} />
//               {portrait && uploadOption == "stl-uploads" && <Image
//                 src={URL.createObjectURL(portrait)}
//                 alt={`Uploaded portrait`}
//                 width={150}
//                 height={150}
//                 className="rounded-[10px] object-cover w-full"
//               />}
//             </div>



//           </div>
//         </div>

//       </FormWrapper>
//     </>
//   );
// };

// export default Scan;
