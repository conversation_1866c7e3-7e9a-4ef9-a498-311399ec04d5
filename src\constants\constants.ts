import { Column } from "@/components/reuseable/ReuseableTable";
import person from "../../public/images/Ellipse 2.png";

export const columns: Column[] = [
  { accessor: "patient", label: "Patient" },
  { accessor: "startDate", label: "Start Date" },
  { accessor: "treatmentOption", label: "Treatment Option" },
  { accessor: "status", label: "Status" },
  { accessor: "notes", label: "Notes" },
  { accessor: "daysSinceLastUpdate", label: "Days Since Last Update" },
  { accessor: "country", label: "Country" },
];
export const newPatients = [
  {
    patient: {
      image: person.src.toString(),
      name: "<PERSON><PERSON><PERSON>, Bedoor",
      id: "#16958",
    },
    startDate: "23/10/2022",
    treatmentOption: "4D Graphy Retainer",
    status: "Aligner Shipped",
    notes: "teeth 18, 28 to be extracted (do not show them in the model)",
    daysSinceLastUpdate: 2,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "<PERSON>", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "4D Graphy Retainer",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "4D Graphy Retainer",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: {
      image: person.src.toString(),
      name: "Fatima Zahra",
      id: "#16962",
    },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: {
      image: person.src.toString(),
      name: "Ailaithan, Bedoor",
      id: "#16958",
    },
    startDate: "23/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "teeth 18, 28 to be extracted (do not show them in the model)",
    daysSinceLastUpdate: 2,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: {
      image: person.src.toString(),
      name: "Fatima Zahra",
      id: "#16962",
    },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: {
      image: person.src.toString(),
      name: "Ailaithan, Bedoor",
      id: "#16958",
    },
    startDate: "23/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "teeth 18, 28 to be extracted (do not show them in the model)",
    daysSinceLastUpdate: 2,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: {
      image: person.src.toString(),
      name: "Fatima Zahra",
      id: "#16962",
    },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: {
      image: person.src.toString(),
      name: "Ailaithan, Bedoor",
      id: "#16958",
    },
    startDate: "23/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "teeth 18, 28 to be extracted (do not show them in the model)",
    daysSinceLastUpdate: 2,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: {
      image: person.src.toString(),
      name: "Fatima Zahra",
      id: "#16962",
    },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
];

export const oldPatients = [
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Omar Farooq", id: "#16963" },
    startDate: "20/08/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Sara Ali", id: "#16964" },
    startDate: "12/07/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor for any discomfort.",
    daysSinceLastUpdate: 4,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Ahmed Raza", id: "#16965" },
    startDate: "25/06/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper tracking of aligners.",
    daysSinceLastUpdate: 6,
    patientinitials: "High",
  },
];

export const archived = [
  {
    patient: {
      image: person.src.toString(),
      name: "Ailaithan, Bedoor",
      id: "#16958",
    },
    startDate: "23/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "teeth 18, 28 to be extracted (do not show them in the model)",
    daysSinceLastUpdate: 2,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: {
      image: person.src.toString(),
      name: "Fatima Zahra",
      id: "#16962",
    },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: {
      image: person.src.toString(),
      name: "Ailaithan, Bedoor",
      id: "#16958",
    },
    startDate: "23/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "teeth 18, 28 to be extracted (do not show them in the model)",
    daysSinceLastUpdate: 2,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: {
      image: person.src.toString(),
      name: "Fatima Zahra",
      id: "#16962",
    },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: {
      image: person.src.toString(),
      name: "Ailaithan, Bedoor",
      id: "#16958",
    },
    startDate: "23/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "teeth 18, 28 to be extracted (do not show them in the model)",
    daysSinceLastUpdate: 2,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: {
      image: person.src.toString(),
      name: "Fatima Zahra",
      id: "#16962",
    },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: {
      image: person.src.toString(),
      name: "Ailaithan, Bedoor",
      id: "#16958",
    },
    startDate: "23/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "teeth 18, 28 to be extracted (do not show them in the model)",
    daysSinceLastUpdate: 2,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "John Doe", id: "#16959" },
    startDate: "01/11/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Ensure proper alignment of teeth 12 and 22.",
    daysSinceLastUpdate: 5,
    patientinitials: "High",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src.toString(), name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: {
      image: person.src.toString(),
      name: "Fatima Zahra",
      id: "#16962",
    },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src, name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: person.src, name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
  {
    patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
    startDate: "15/09/2022",
    treatmentOption: "Advanced Package",
    status: "Completed",
    notes: "Treatment completed successfully.",
    daysSinceLastUpdate: 0,
    patientinitials: "",
  },
  {
    patient: { image: "", name: "Ali Khan", id: "#16961" },
    startDate: "10/10/2022",
    treatmentOption: "Comprehensive Package",
    status: "Aligner Shipped",
    notes: "Monitor progress closely.",
    daysSinceLastUpdate: 3,
    patientinitials: "Medium",
  },
  {
    patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
    startDate: "05/12/2022",
    treatmentOption: "Basic Package",
    status: "Aligner Shipped (Track)",
    notes: "Adjust aligners for better fit.",
    daysSinceLastUpdate: 7,
    patientinitials: "Low",
  },
];

// Retainer

export const retainercolumn: Column[] = [
  { accessor: "patient", label: "Patient" },
  { accessor: "startDate", label: "Start Date" },
  { accessor: "treatmentOption", label: "Treatment Option" },
  { accessor: "status", label: "Status" },
  { accessor: "notes", label: "Notes" },
  { accessor: "daysSinceLastUpdate", label: "Days Since Last Update" },
  { accessor: "country", label: "Country" },
];
//   {
//     patient: {
//       image: person.src.toString(),
//       name: "Ailaithan, Bedoor",
//       id: "#16958",
//     },
//     startDate: "23/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "teeth 18, 28 to be extracted (do not show them in the model)",
//     daysSinceLastUpdate: 2,
//     patientinitials: "",
//   },
//   {
//     patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
//     startDate: "01/11/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Ensure proper alignment of teeth 12 and 22.",
//     daysSinceLastUpdate: 5,
//     patientinitials: "High",
//   },
//   {
//     patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
//     startDate: "01/11/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Ensure proper alignment of teeth 12 and 22.",
//     daysSinceLastUpdate: 5,
//     patientinitials: "High",
//   },
//   {
//     patient: { image: person.src, name: "John Doe", id: "#16959" },
//     startDate: "01/11/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Ensure proper alignment of teeth 12 and 22.",
//     daysSinceLastUpdate: 5,
//     patientinitials: "High",
//   },
//   {
//     patient: { image: person.src, name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: "", name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: person.src.toString(), name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: {
//       image: person.src.toString(),
//       name: "Fatima Zahra",
//       id: "#16962",
//     },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: { image: person.src, name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: person.src, name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: "", name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: {
//       image: person.src.toString(),
//       name: "Ailaithan, Bedoor",
//       id: "#16958",
//     },
//     startDate: "23/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "teeth 18, 28 to be extracted (do not show them in the model)",
//     daysSinceLastUpdate: 2,
//     patientinitials: "",
//   },
//   {
//     patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
//     startDate: "01/11/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Ensure proper alignment of teeth 12 and 22.",
//     daysSinceLastUpdate: 5,
//     patientinitials: "High",
//   },
//   {
//     patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
//     startDate: "01/11/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Ensure proper alignment of teeth 12 and 22.",
//     daysSinceLastUpdate: 5,
//     patientinitials: "High",
//   },
//   {
//     patient: { image: person.src, name: "John Doe", id: "#16959" },
//     startDate: "01/11/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Ensure proper alignment of teeth 12 and 22.",
//     daysSinceLastUpdate: 5,
//     patientinitials: "High",
//   },
//   {
//     patient: { image: person.src, name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: "", name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: person.src.toString(), name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: {
//       image: person.src.toString(),
//       name: "Fatima Zahra",
//       id: "#16962",
//     },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: { image: person.src, name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: person.src, name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: "", name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: {
//       image: person.src.toString(),
//       name: "Ailaithan, Bedoor",
//       id: "#16958",
//     },
//     startDate: "23/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "teeth 18, 28 to be extracted (do not show them in the model)",
//     daysSinceLastUpdate: 2,
//     patientinitials: "",
//   },
//   {
//     patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
//     startDate: "01/11/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Ensure proper alignment of teeth 12 and 22.",
//     daysSinceLastUpdate: 5,
//     patientinitials: "High",
//   },
//   {
//     patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
//     startDate: "01/11/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Ensure proper alignment of teeth 12 and 22.",
//     daysSinceLastUpdate: 5,
//     patientinitials: "High",
//   },
//   {
//     patient: { image: person.src, name: "John Doe", id: "#16959" },
//     startDate: "01/11/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Ensure proper alignment of teeth 12 and 22.",
//     daysSinceLastUpdate: 5,
//     patientinitials: "High",
//   },
//   {
//     patient: { image: person.src, name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: "", name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: person.src.toString(), name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: {
//       image: person.src.toString(),
//       name: "Fatima Zahra",
//       id: "#16962",
//     },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: { image: person.src, name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: person.src, name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: "", name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: {
//       image: person.src.toString(),
//       name: "Ailaithan, Bedoor",
//       id: "#16958",
//     },
//     startDate: "23/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "teeth 18, 28 to be extracted (do not show them in the model)",
//     daysSinceLastUpdate: 2,
//     patientinitials: "",
//   },
//   {
//     patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
//     startDate: "01/11/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Ensure proper alignment of teeth 12 and 22.",
//     daysSinceLastUpdate: 5,
//     patientinitials: "High",
//   },
//   {
//     patient: { image: person.src.toString(), name: "John Doe", id: "#16959" },
//     startDate: "01/11/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Ensure proper alignment of teeth 12 and 22.",
//     daysSinceLastUpdate: 5,
//     patientinitials: "High",
//   },
//   {
//     patient: { image: person.src, name: "John Doe", id: "#16959" },
//     startDate: "01/11/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Ensure proper alignment of teeth 12 and 22.",
//     daysSinceLastUpdate: 5,
//     patientinitials: "High",
//   },
//   {
//     patient: { image: person.src, name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: "", name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: person.src.toString(), name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: {
//       image: person.src.toString(),
//       name: "Fatima Zahra",
//       id: "#16962",
//     },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: { image: person.src, name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: person.src, name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
//   {
//     patient: { image: person.src.toString(), name: "Jane Smith", id: "#16960" },
//     startDate: "15/09/2022",
//     treatmentOption: "Advanced Package",
//     status: "Completed",
//     notes: "Treatment completed successfully.",
//     daysSinceLastUpdate: 0,
//     patientinitials: "",
//   },
//   {
//     patient: { image: "", name: "Ali Khan", id: "#16961" },
//     startDate: "10/10/2022",
//     treatmentOption: "Comprehensive Package",
//     status: "Aligner Shipped",
//     notes: "Monitor progress closely.",
//     daysSinceLastUpdate: 3,
//     patientinitials: "Medium",
//   },
//   {
//     patient: { image: person.src, name: "Fatima Zahra", id: "#16962" },
//     startDate: "05/12/2022",
//     treatmentOption: "Basic Package",
//     status: "Aligner Shipped (Track)",
//     notes: "Adjust aligners for better fit.",
//     daysSinceLastUpdate: 7,
//     patientinitials: "Low",
//   },
// ];
