"use client"
import arrowLeft from "../../../public/svgs/arrow-right-02.svg"
import Image from "next/image"

interface props {
    text?: string
    withArrow?: boolean
    onClick?: () => void

}

const BackButton: React.FC<props> = ({ text = "Back", onClick, withArrow = true }) => {
    return (
        <button
            onClick={onClick}
            className="flex items-center justify-center gap-2 py-4 cursor-pointer rounded-full border border-gray min-w-[120px]">
            {withArrow && <Image src={arrowLeft} alt="Arrow left" className="w-6 h-6"  width={1000} height={1000} />}
            <span className="font-semibold text-lg text-dark">{text}</span>
        </button>

    )
}

export default BackButton