"use client"
import '@/app/globals.css';
// import BreadCrumbs from '@/components/layouts/BreadCrumb';
import Header from '@/components/reuseable/Header';
import { usePathname } from "next/navigation";


export default function DefaultLayout({ children }: { children: React.ReactNode }) {
    const location = usePathname()
    console.log("location", location)
    return (
        <div className="py-4 xl:px-12 px-8 flex-grow flex flex-col">
            <Header onSearchChange={() => { }} searchValue={""} />
            <div className='pt-6 pb-2 flex-grow flex flex-col'>{children}</div>
        </div >
    );
}