"use client"

import React, { useEffect } from "react";
import RoundSelect from "../reuseable/RoundSelect";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { LastNameOptions, LocationOptions, TimeFrameOptions, TreatmentOptions } from "./Options";
import RoundCheckboxRegister from "../reuseable/RoundCheckBoxRegister";

export const SummarySchema = z.object({
    criteria: z.enum(["Last Name", "First Name", "Case ID"], { required_error: "Please Select an option" }),
    includeSecondary: z.boolean(),
    treatmentType: z.enum(["All type of treatment", "Orthodontic", "Surgical"], { required_error: "Please Select an option" }),
    location: z.enum(["All locations", "Location 1", "Location 2"], { required_error: "Please Select an option" }),
    timeFrame: z.enum(["This week", "Last week", "This month"], { required_error: "Please Select an option" })
})
// .superRefine((data, ctx) => {
//     //   if (data.criteria === "") {
//     //     ctx.addIssue({
//     //       path: ['extractionTeeth'],
//     //       code: z.ZodIssueCode.custom,
//     //       message: 'Please specify the teeth',
//     //     });
//     //   }
// });
interface SummaryTabProps {
    onFilterChange?: (filters: {
        criteria: string;
        includeSecondary: boolean;
        treatmentType: string;
        location: string;
        timeFrame: string;
    }) => void;
}



export type SummarySchemaType = z.infer<typeof SummarySchema>;

const SummaryTab: React.FC<SummaryTabProps> = ({  }) => {
    // const [filters, setFilters] = useState({
    //     criteria: "Last Name",
    //     includeSecondary: false,
    //     treatmentType: "All type of treatment",
    //     location: "All locations",
    //     timeFrame: "This week",
    // });

    // const handleChange = (key: string, value: string | boolean) => {
    //     const updatedFilters = { ...filters, [key]: value };
    //     setFilters(updatedFilters);
    //     if (onFilterChange) {
    //         onFilterChange(updatedFilters);
    //     }
    // };

    const {
        register,
        watch,
        // formState: { errors },
    } = useForm<SummarySchemaType>({
        resolver: zodResolver(SummarySchema),
        defaultValues: {
            criteria: "Last Name",
            includeSecondary: false,
            treatmentType: "All type of treatment",
            location: "All locations",
            timeFrame: "This week"
        },
        mode: "onChange"
    },
    );

    const data = watch()
    useEffect(() => {
        console.log(data)
    }, [data])

    return (
        <div className=" p-6 bg-white rounded-lg">

            <div className="w-1/2">
                <h2 className="text-lg font-semibold">
                    Show approved cases that meet the following criteria
                </h2>
                <div className="my-6">
                    <RoundSelect
                        id="criteria-select"
                        register={register("criteria")}
                        options={LastNameOptions}
                    />
                </div>

                <h2 className="text-lg font-semibold">Show for this type of treatment</h2>
                <div className="mt-2 mb-6 flex items-center gap-2">
                    <RoundCheckboxRegister id="include-secondary" label="Include secondary treatments" register={register("includeSecondary")} />
                </div>

                <div className="mb-4">
                    <RoundSelect
                        id="treatment-select"
                        register={register("treatmentType")}
                        options={TreatmentOptions}
                    />
                </div>

                <div className="mb-4">
                    <RoundSelect
                        id="location-select"
                        register={register("location")}
                        options={LocationOptions}
                    />
                </div>

                <div>

                    <RoundSelect
                        id="timeframe-select"
                        register={register("timeFrame")}
                        options={TimeFrameOptions}
                    />
                </div>

            </div>

        </div>
    );
};

export default SummaryTab;