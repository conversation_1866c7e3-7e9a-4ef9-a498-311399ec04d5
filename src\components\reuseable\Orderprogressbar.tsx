import React from 'react';
import CheckCircle from './Icons/CheckCircle';
import Circle from './Icons/Circle';

type StepItem = {
    label: string;
    completed: boolean;
};

type Step = {
    title: string | React.ReactNode;
    items: StepItem[];
};

interface ProgressProps {
    currentStep: number; // 0-indexed
}

const steps: Step[] = [
    {
        title: 'All materials received',
        items: [
            { label: 'Submit prescription', completed: true },
            { label: 'Finish prescription', completed: true },
            { label: "Waiting for patient's records", completed: false },
            { label: 'Evaluating records', completed: false },
            { label: 'All materials received', completed: false },
        ],
    },
    {
        title: 'Treatment plan approved',
        items: [
            { label: 'Developing treatment plan', completed: true },
            { label: 'Review treatment plan', completed: true },
            { label: 'Treatment plan approved', completed: true },
        ],
    },
    {
        title: (
            <>
                <PERSON><PERSON>rs shipped{' '}
                <span className="text-orange-500 font-medium">(Track)</span>
            </>
        ),
        items: [
            { label: 'Manufacturing aligners', completed: true },
            { label: '<PERSON><PERSON><PERSON> shipped', completed: true },
        ],
    },
];

const Orderprogressbar: React.FC<ProgressProps> = ({ currentStep }) => {
    return (
        <>

            <div className="w-full py-2 m-2 border-y-1 border-solid border-[#999999af]">
                <div className='flex items-center justify-between'>
                    <div>
                        <h2 className="text-md font-semibold mb-4">Order Progress</h2>
                    </div>
                    <div >
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M20.7929 17.707L11.9999 8.914L3.20688 17.707C2.81638 18.0975 2.18338 18.0975 1.79287 17.707C1.40237 17.3165 1.40237 16.6835 1.79287 16.293L11.2929 6.793C11.4884 6.5975 11.7439 6.5 11.9999 6.5C12.2559 6.5 12.5114 6.5975 12.7069 6.793L22.2069 16.293C22.5974 16.6835 22.5974 17.3165 22.2069 17.707C21.8164 18.0975 21.1834 18.0975 20.7929 17.707Z" fill="#999999" />
                        </svg>
                    </div>
                </div>

                {/* Progress Line */}
                <div className="relative w-full flex justify-between items-center mb-4 px-6">
                    <div className="absolute top-4 left-6 right-6 h-1 bg-gray-300 z-0">
                        <div
                            className="h-1 bg-orange-500 transition-all duration-300"
                            style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
                        />
                    </div>

                    {/* Step Circles */}
                    {steps.map((_, index) => (
                        <div
                            key={index}
                            className={`z-10 w-8 h-8 rounded-full flex items-center justify-center border-2 text-sm font-bold ${index <= currentStep
                                ? 'bg-orange-500 border-orange-500 text-white'
                                : 'bg-white border-gray-300 text-gray-400'
                                }`}
                        >
                            {index < currentStep ? '✓' : index === currentStep ? '✓' : ''}
                        </div>
                    ))}
                </div>

                {/* Step Texts */}
                <div className="grid grid-cols-3 gap-2">
                    {steps.map((step, idx) => (
                        <div key={idx}>
                            <h3 className="font-medium text-md text-gray-800 mb-4">{step.title}</h3>
                            <ul className="space-y-2">
                                {step.items.map((item, iIdx) => (
                                    <li
                                        key={iIdx}
                                        className={`flex items-center text-sm ${item.completed ? 'text-gray-800' : 'text-gray-400'
                                            }`}
                                    >
                                        <span className="mr-2">
                                            {item.completed ? (
                                                <span className="text-green-500"><CheckCircle /></span>
                                            ) : (
                                                // <span className="w-4 h-4 border border-gray-400 rounded-full inline-block" />
                                                <Circle />
                                            )}
                                        </span>
                                        {item.label}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ))}
                </div>
            </div>
        </>
    );
};

export default Orderprogressbar;
