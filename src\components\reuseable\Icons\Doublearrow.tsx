import React from 'react'
type ArrowIconProps = {
    isOpen: boolean;
    className?: string;
};
const Doublearrow: React.FC<ArrowIconProps> = ({ isOpen, className }) => {
    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            className={`transition-transform duration-300 ${isOpen ? '' : 'rotate-180'} ${className}`}
        >
            <path
                d="M11.5 18C11.5 18 5.50001 13.5811 5.5 12C5.49999 10.4188 11.5 6 11.5 6"
                stroke="#7B7B7B"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
            <path
                d="M18.5 18C18.5 18 12.5 13.5811 12.5 12C12.5 10.4188 18.5 6 18.5 6"
                stroke="#7B7B7B"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
            />
        </svg>
    );
}

export default Doublearrow
