import React from 'react'
import { UseFormRegisterReturn } from 'react-hook-form'

interface CustomInputProps {
  type: string
  placeholder?: string
  error?: string
  register?: UseFormRegisterReturn // React Hook Form's `register` function
  className?: string
  containerClassName?: string,
  min?: number,
  max?: number,
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void
}

const CustomInput: React.FC<CustomInputProps> = ({ type, placeholder, error, register, className, containerClassName, min, max, onChange }) => {
  return (
    <div className={`${containerClassName}`}>
      <input
        min={min}
        max={max}
        type={type}
        {...register}
        onChange={onChange}
        placeholder={placeholder}
        className={`w-full px-4 py-2 border ${error ? 'border-red-500' : 'border-gray/40 '} rounded-[60px] outline-none ${className}`}
      />
      {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
    </div>
  )
}

export default CustomInput