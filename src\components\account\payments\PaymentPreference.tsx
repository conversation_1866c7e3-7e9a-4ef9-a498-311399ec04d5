"use client"
import masterCard from "../../../../public/svgs/mastercard.svg"
import Image from "next/image"
import Link from "next/link"

const PaymentPreference = () => {
    return (
        <div className=" p-6 bg-white rounded-xl col-span-1">
            <div className="flex flex-col gap-2">
                <div className="flex items-center justify-between">
                    <h3 className="font-bold text-dark text-[1.1rem]">Account Payment Preference</h3>
                    <Link href={"/account/payments/preference"} className="text-primary font-semibold text-[1.1rem] cursor-pointer">Edit</Link>
                </div>
                <div className="flex flex-col gap-2">
                    <div className="flex items-center justify-between">
                        <h3 className="font-medium text-gray text-[1rem]">Payment Method</h3>
                        <button className="text-primary font-semibold text-[1.1rem]">Edit</button>
                    </div>
                    <div className="flex items-center gap-2">
                        <div className="relative h-5 w-8">
                            <Image fill quality={100} className="object-cover" alt="card image" src={masterCard} />
                        </div>
                        <h3 className="text-primary font-semibold text-[1.1rem]">Ending in 6634Expires: Oct 2025</h3>
                    </div>
                </div>
                <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray text-[1rem]">Invoice Preference</h3>
                    <button className="text-primary font-semibold text-[1.1rem]">Edit</button>
                </div>
                <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray text-[1rem]">Send email notification of invoice availability</h3>
                </div>
                <div className="border-b border-b-gray my-4"></div>

                <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray text-[1rem]">Unpaid Balance</h3>
                    <h3 className="font-medium text-gray text-[1rem]"> ر.س 9014.00</h3>
                </div>
                <div className="flex items-center justify-between">
                    <h3 className="font-medium text-gray text-[1rem]">Available credit : </h3>
                    <h3 className="font-medium text-gray text-[1rem]"> ر.س0.00</h3>
                </div>
            </div>
            {/* <RoundCheckbox id="include-secondary" label="AlOqair street, Al Hofuf, 36365, SA (#1434279)" value="AlOqair street, Al Hofuf, 36365, SA (#1434279)" onChange={(e) => setBillingAddress(e.target.value)}/> */}
        </div>
    )
}

export default PaymentPreference