'use client'
import { useRouter } from 'next/navigation';
import React, { useState } from 'react'

const PreviewPage = () => {
    const [modalOpen, setModalOpen] = useState(false);
    const [actionType, setActionType] = useState<"approve" | "reject" | null>(null);
    const [reason, setReason] = useState("");

    const navigate = useRouter()
    const handleOpenModal = (type: "approve" | "reject") => {
        setActionType(type);
        setReason("");
        setModalOpen(true);
    };

    const handleCloseModal = () => {
        setModalOpen(false);
        setActionType(null);
        setReason("");
    };

    const handleSubmit = () => {
        console.log({
            action: actionType,
            reason: reason
        });
        navigate.push("/dashboard")
        handleCloseModal();
    };

    return (
        <div>
            <div className='relative'>
                <div className='w-full h-[100vh]'>
                    <iframe
                        src="https://downloads-default.nemocloud-services.com/DownloadUploadService/nemobox/viewer/shared/advanced?centerId=158-57-87-89&docId=112ab99c-4426-41c3-b5ef-8a6355af126a&userId=150-00-53-13"
                        width="100%"
                        height="100%"
                        style={{ border: "none" }}
                        allowFullScreen
                        title="NemoCloud Viewer"
                    />
                </div>
                <div className="flex gap-4 mt-3 justify-center absolute top-0 right-[23%]">
                    <button
                        className="px-4 py-1.5 bg-orange-500 text-white rounded "
                        onClick={() => handleOpenModal("approve")}
                    >
                        Accept
                    </button>
                    <button
                        className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                        onClick={() => handleOpenModal("reject")}
                    >
                        Reject
                    </button>
                </div>
                {/* Modal */}
                {modalOpen && (
                    <div className="fixed inset-0 flex items-center justify-center bg-black/70 z-50">
                        <div className="bg-white rounded-lg shadow-lg p-6 w-[500px]">
                            <h2 className="text-lg font-semibold mb-4">
                                Reason of {actionType === "approve" ? "Approve" : "Reject"}
                            </h2>
                            <textarea
                                className="w-full border rounded p-2 mb-4"
                                rows={4}
                                placeholder={`Enter reason for ${actionType === "approve" ? "approval" : "rejection"}...`}
                                value={reason}
                                onChange={e => setReason(e.target.value)}
                            />
                            <div className="flex justify-end gap-2">
                                <button
                                    className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400"
                                    onClick={handleCloseModal}
                                >
                                    Cancel
                                </button>
                                <button
                                    className="px-4 py-2 bg-orange-500 text-white rounded "
                                    onClick={handleSubmit}
                                >
                                    Submit
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}

export default PreviewPage