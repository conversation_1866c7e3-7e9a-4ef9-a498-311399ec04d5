import {
    AnimatePresence
    // , motion
} from "framer-motion";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import { SpecialProps } from "./MovementResctiction_2";
// import { DefaultTransition } from "./ArchToTreat_1";


const AnteriorEdgeToEdge: React.FC<SpecialProps<{ option: string; extrudeAnteriors?: string }>> = ({ register,
    // errors, watch
}) => {
    // const option = watch("anteriorEdgeToEdge.option")
    return (
        <div>
            <p className="text-lg font-semibold text-gray-800">9: Anterior Edge to Edge Occlusion</p>
            <div className="mt-3 flex gap-3">
                <RoundRadioButton
                    id="anterior-edge-correct"
                    label="Do not correct"
                    value="doNotCorrect"
                    register={register}
                    name="anteriorEdgeToEdge.option"
                    labelClass='!text-[#434343] text-base'
                />
                <RoundRadioButton
                    id="anterior-edge-not-correct"
                    label="Correct"
                    value="correct"
                    register={register}
                    name="anteriorEdgeToEdge.option"
                    labelClass='!text-[#434343] text-base'
                />
            </div>
            <AnimatePresence initial={false} mode="wait">
                {/* {option == "correct" && (
                    <motion.div
                        key="upper"
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={DefaultTransition}
                        style={{ overflow: 'hidden' }}
                    >
                        <div className="ps-4 flex gap-3 mt-3">
                            <RoundRadioButton
                                id="extrude-upper-anteriors"
                                label="Extrude Upper Anteriors"
                                value="extrudeUpperAnteriors"
                                register={register}
                                name="anteriorEdgeToEdge.extrudeAnteriors"
                                labelClass='!text-[#434343] text-base'
                            />

                            <RoundRadioButton
                                id="extrude-lower-anteriors"
                                label="Extrude Lower Anteriors"
                                value="extrudeLowerAnteriors"
                                register={register}
                                name="anteriorEdgeToEdge.extrudeAnteriors"
                                labelClass='!text-[#434343] text-base'
                            />

                            <RoundRadioButton
                                id="intrude-upper-posteriors"
                                label="Intrude Upper Posteriors"
                                value="intrudeUpperPosteriors"
                                register={register}
                                name="anteriorEdgeToEdge.extrudeAnteriors"
                                labelClass='!text-[#434343] text-base'
                            />

                            <RoundRadioButton
                                id="intrude-lower-posteriors"
                                label="Intrude Lower Posteriors"
                                value="intrudeLowerPosteriors"
                                register={register}
                                name="anteriorEdgeToEdge.extrudeAnteriors"
                                labelClass='!text-[#434343] text-base'
                            />
                             {errors.anteriorEdgeToEdge?.option && (
                                <p className="text-red-500 text-sm mt-1">{errors.anteriorEdgeToEdge?.option.message}</p>
                            )} 
                        </div>
                    </motion.div>
                )} */}
            </AnimatePresence>
        </div>
    )
}

export default AnteriorEdgeToEdge