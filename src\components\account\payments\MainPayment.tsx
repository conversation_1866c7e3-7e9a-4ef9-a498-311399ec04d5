"use client"

import React from "react";
import { z } from "zod";
import InvoiceBilling from "./InvoiceBilling";
import PaymentPreference from "./PaymentPreference";
import CustomButton from "./CustomButton";
import checkIcon from "../../../../public/svgs/icons8_payment_check 1.svg"
import exportIcon from "../../../../public/svgs/icons8_export 1.svg"
import InvoicesDataTable from "./InvoicesDataTable";


export const SummarySchema = z.object({
    criteria: z.enum(["Last Name", "First Name", "Case ID"], { required_error: "Please Select an option" }),
    includeSecondary: z.boolean(),
    treatmentType: z.enum(["All type of treatment", "Orthodontic", "Surgical"], { required_error: "Please Select an option" }),
    location: z.enum(["All locations", "Location 1", "Location 2"], { required_error: "Please Select an option" }),
    timeFrame: z.enum(["This week", "Last week", "This month"], { required_error: "Please Select an option" })
})





export type SummarySchemaType = z.infer<typeof SummarySchema>;

const MainPayments = () => {


    return (
        <div>
            <div className="flex flex-col gap-3">
                <div className="grid grid-cols-2 gap-3">
                    <InvoiceBilling />
                    <PaymentPreference />
                </div>
                <div className="flex items-center justify-end gap-2">
                    <CustomButton text="Payment History & Statements" icon={checkIcon} onClick={() => { }} />
                    <CustomButton text="Export As Excel" icon={exportIcon} onClick={() => { }} />
                </div>
                <div className="my-4">
                    <p>
                        <span className="font-bold text-gray">Please note:</span>
                        <span className="text-gray font-normal">It will be possible to download a PDF copy of your invoice within 3-4 days after its creation.</span>
                    </p>
                </div>
                <InvoicesDataTable />
            </div>
        </div>
    );
};

export default MainPayments;