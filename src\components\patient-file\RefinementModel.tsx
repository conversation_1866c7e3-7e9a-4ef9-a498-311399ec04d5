"use client"

import React from "react";
import Image from "next/image"
import closeIcon from "../../../public/svgs/icons8_multiply 1.svg"
import { useState } from "react";
import RefinementDetails from "./RefinementDetail";
import Scan from "./Scan";
import Photos from "./Photos";
import { toast } from "react-toastify";

interface props {
    onClose: () => void
}

export type TabName = "details" | "scan" | "photos" | "summary";

type Tab = {
    details: number,
    scan: number,
    photos: number,
    summary: number
}

// Define interface for details data
interface RefinementDetailsData {
    isTreatmentComplete: string;
    lastUpperTrackingNumber?: number;
    lastLowerTrackingNumber?: number;
    reason: {
        reasons: string[];
        otherReason?: string;
    };
    note?: string;
}

// Define interface for scan data
interface ScanData {
    upperSTL?: File;
    upperSTLName?: string;
    lowerSTL?: File;
    lowerSTLName?: string;
}

// Define interface for photo data
interface DentalPhoto {
    name: string;
    file: File | null;
}

// Define interface for radiograph data
interface RadiographData {
    radiograph1: File | null;
    radiograph2: File | null;
}

const tabs: Tab = { details: 1, scan: 2, photos: 3, summary: 4 }

// Summary component receives data as props
// Summary component receives data as props
const Summary = ({
    setTab,
    handleFinalSubmit,
    detailsData,
    scanData,
    photosData,
    radiographData
}: {
    setTab: (tab: TabName, force?: boolean) => void,
    handleFinalSubmit: () => void,
    detailsData: RefinementDetailsData,
    scanData: ScanData,
    photosData: DentalPhoto[],
    radiographData: RadiographData
}) => {
    // Create object URLs for photos
    const photoUrls = photosData.reduce((acc, photo) => {
        if (photo.file) {
            console.log("🚀 ~ photoUrls ~ photo:", photo)
            acc[photo.name] = URL.createObjectURL(photo.file);

        }
        return acc;
    }, {} as Record<string, string>);

    // Create object URLs for radiographs
    const radiographUrls = {
        radiograph1: radiographData.radiograph1 ? URL.createObjectURL(radiographData.radiograph1) : null,
        radiograph2: radiographData.radiograph2 ? URL.createObjectURL(radiographData.radiograph2) : null
    };

    // Cleanup object URLs when component unmounts
    React.useEffect(() => {
        return () => {
            Object.values(photoUrls).forEach(url => URL.revokeObjectURL(url));
            if (radiographUrls.radiograph1) URL.revokeObjectURL(radiographUrls.radiograph1);
            if (radiographUrls.radiograph2) URL.revokeObjectURL(radiographUrls.radiograph2);
        };
    }, [photoUrls, radiographUrls]);

    return (
        <div className="p-2">
            <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-lg">Summary</h3>

            </div>

            <div className="flex-grow bg-gray-50 p-4 rounded-lg space-y-4">
                {/* Refinement Details Section */}
                <div className="bg-white p-4 rounded-md border border-gray-200 mb-4">
                    <div className="flex justify-between items-center mb-3">
                        <h4 className="font-semibold text-gray-700">Refinement Details</h4>
                        <button
                            type="button"
                            onClick={() => setTab("details", true)}
                            className="text-primary hover:underline flex items-center"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M12 20h9"></path>
                                <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                            </svg>
                            <span className="ml-1">Edit</span>
                        </button>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <p className="text-sm text-gray-500">Treatment Complete</p>
                            <p className="font-medium">{detailsData.isTreatmentComplete === "yes" ? "Yes" : "No"}</p>
                        </div>

                        {detailsData.isTreatmentComplete === "no" && (
                            <>
                                <div>
                                    <p className="text-sm text-gray-500">Last Upper Tracking #</p>
                                    <p className="font-medium">{detailsData.lastUpperTrackingNumber}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-500">Last Lower Tracking #</p>
                                    <p className="font-medium">{detailsData.lastLowerTrackingNumber}</p>
                                </div>
                            </>
                        )}

                        <div className="col-span-2">
                            <p className="text-sm text-gray-500">Reason for Refinement</p>
                            <div className="mt-1">
                                {detailsData.reason.reasons.map((reason, index) => (
                                    <p key={index} className="text-sm font-medium">
                                        {reason === "other" ? detailsData.reason.otherReason : reason}
                                    </p>
                                ))}
                            </div>
                        </div>
                    </div>

                    {detailsData.note && (
                        <div className="mt-3">
                            <p className="text-sm text-gray-500">Additional Notes</p>
                            <p className="p-2 bg-gray-50 rounded-md mt-1">{detailsData.note}</p>
                        </div>
                    )}
                </div>

                {/* Scan Section */}
                <div className="bg-white p-4 rounded-md border border-gray-200 mb-4">
                    <div className="flex justify-between items-center mb-3">
                        <h4 className="font-semibold text-gray-700">Scan Information</h4>
                        <button
                            type="button"
                            onClick={() => setTab("scan", true)}
                            className="text-primary hover:underline flex items-center"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M12 20h9"></path>
                                <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                            </svg>
                            <span className="ml-1">Edit</span>
                        </button>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                        {scanData.upperSTL && (
                            <div>
                                <p className="text-sm text-gray-500">Upper STL</p>
                                <p className="font-medium">{scanData.upperSTLName || scanData.upperSTL.name}</p>
                            </div>
                        )}

                        {scanData.lowerSTL && (
                            <div>
                                <p className="text-sm text-gray-500">Lower STL</p>
                                <p className="font-medium">{scanData.lowerSTLName || scanData.lowerSTL.name}</p>
                            </div>
                        )}
                    </div>
                </div>

                {/* Photos Section */}
                <div className="bg-white p-4 rounded-md border border-gray-200">
                    <div className="flex justify-between items-center mb-3">
                        <h4 className="font-semibold text-gray-700">Optional Photos</h4>
                        <button
                            type="button"
                            onClick={() => setTab("photos", true)}
                            className="text-primary hover:underline flex items-center"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M12 20h9"></path>
                                <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                            </svg>
                            <span className="ml-1">Edit</span>
                        </button>
                    </div>

                    {photosData.filter(photo => photo.file !== null).length > 0 ? (
                        <div className="grid grid-cols-3 gap-3">
                            {photosData.filter(photo => photo.file !== null).map((photo, idx) => (
                                <div key={idx} className="text-center">
                                    <div className="bg-gray-100 rounded-md p-2 h-24 overflow-hidden flex items-center justify-center">
                                        {photoUrls[photo.name] && (
                                            <Image
                                                src={photoUrls[photo.name]}
                                                alt={photo.name}
                                                className="w-full h-full object-contain"
                                                width={1000}
                                                height={1000}
                                            />
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <p className="text-gray-500">No photos uploaded</p>
                    )}

                    {/* Radiographs */}
                    {(radiographData.radiograph1 || radiographData.radiograph2) && (
                        <div className="mt-4">
                            <h5 className="font-medium text-gray-700 mb-2">Radiographs</h5>
                            <div className="grid grid-cols-2 gap-3">
                                {radiographData.radiograph1 && radiographUrls.radiograph1 && (
                                    <div className="text-center">
                                        <div className="bg-gray-100 rounded-md p-2 h-24 overflow-hidden flex items-center justify-center">
                                            <Image
                                                width={1000}
                                                height={1000}
                                                src={radiographUrls.radiograph1}
                                                alt="Radiograph 1"
                                                className="w-full h-full object-contain"
                                            />
                                        </div>
                                    </div>
                                )}

                                {radiographData.radiograph2 && radiographUrls.radiograph2 && (
                                    <div className="text-center">
                                        <div className="bg-gray-100 rounded-md p-2 h-24 overflow-hidden flex items-center justify-center">
                                            <Image
                                                width={1000}
                                                height={1000}
                                                src={radiographUrls.radiograph2}
                                                alt="Radiograph 2"
                                                className="w-full h-full object-contain"
                                            />
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
            <div className="flex gap-3 justify-end">
                <button
                    type="button"
                    onClick={() => setTab("photos", true)}
                    className="flex items-center justify-center py-2 cursor-pointer rounded-full border border-gray-300 px-6"
                >
                    <span className="font-semibold text-base text-dark">Back</span>
                </button>
                <button
                    type="button"
                    onClick={handleFinalSubmit}
                    className="flex items-center justify-center py-2.5 cursor-pointer rounded-full bg-primary px-6 hover:bg-[#D45A08] transition"
                >
                    <span className="font-semibold text-base text-white">Submit Request</span>
                </button>
            </div>
        </div>
    );
};

const RefinementModel: React.FC<props> = ({ onClose }) => {
    const [currentTab, setCurrentTab] = useState<TabName>("details");

    // State for storing form data
    const [detailsData, setDetailsData] = useState<RefinementDetailsData>({
        isTreatmentComplete: "yes",
        reason: {
            reasons: [],
        },
    });

    const [scanData, setScanData] = useState<ScanData>({});

    const [photosData, setPhotosData] = useState<DentalPhoto[]>([
        { name: "profileRepose", file: null },
        { name: "buccalRight", file: null },
        { name: "buccalLeft", file: null },
        { name: "frontalRepose", file: null },
        { name: "frontalSmiling", file: null },
        { name: "labialAnterior", file: null },
        { name: "occlussalLower", file: null },
        { name: "occlussalUpper", file: null },
        { name: "socialSmile", file: null },
    ]);

    const [radiographData, setRadiographData] = useState<RadiographData>({
        radiograph1: null,
        radiograph2: null
    });

    const switchTab = (tab: TabName, force: boolean = false) => {
        if (tabs[tab] < tabs[currentTab] || force) {
            setCurrentTab(tab);
        }
    };

    const handleFinalSubmit = () => {
        // Combine all data for final submission
        const finalData = {
            details: detailsData,
            scan: {
                upperSTLName: scanData.upperSTLName,
                lowerSTLName: scanData.lowerSTLName
            },
            photos: photosData.filter(photo => photo.file !== null).map(photo => ({
                name: photo.name
            })),
            radiographs: {
                radiograph1: radiographData.radiograph1 ? true : false,
                radiograph2: radiographData.radiograph2 ? true : false
            }
        };

        console.log("Final submission data:", finalData);

        toast.success("New refinement added");
        setTimeout(() => onClose(), 1000);
    };

    return (
        <div className="bg-dark/30 w-screen h-screen fixed top-0 left-0 z-50 flex items-center justify-center">
            <div className="bg-white p-5 rounded-2xl min-w-6xl flex flex-col">
                <div className="flex items-center justify-between">
                    <div className="font-bold text-xl font-manrope text-dark">
                        Refinement
                    </div>
                    <div onClick={() => onClose()} className="w-6 h-6 relative cursor-pointer">
                        <Image fill src={closeIcon} alt="Close icon" />
                    </div>
                </div>

                <div className="my-5 border-y border-y-neutral-300 w-full"></div>

                <div className="flex items-center justify-between gap-6 pb-3">
                    <button onClick={() => switchTab("details")} className="cursor-pointer flex flex-col justify-center items-center gap-1">
                        <span className="bg-primary w-5 h-5 rounded-full flex items-center justify-center">
                            {currentTab === "details" && <span className="bg-white rounded-full w-2.5 h-2.5"></span>}
                        </span>
                        <span>Refinement Details</span>
                    </button>

                    <div className="flex-grow border-t border-t-dark"></div>

                    <button onClick={() => switchTab("scan")} className="cursor-pointer flex flex-col justify-center items-center gap-1">
                        <span className="bg-primary w-5 h-5 rounded-full flex items-center justify-center">
                            {currentTab === "scan" && <span className="bg-white rounded-full w-2.5 h-2.5"></span>}
                        </span>
                        <span>Scan</span>
                    </button>

                    <div className="flex-grow border-t border-t-dark"></div>

                    <button onClick={() => switchTab("photos")} className="cursor-pointer flex flex-col justify-center items-center gap-1">
                        <span className="bg-primary w-5 h-5 rounded-full flex items-center justify-center">
                            {currentTab === "photos" && <span className="bg-white rounded-full w-2.5 h-2.5"></span>}
                        </span>
                        <span>Optional photos</span>
                    </button>

                    <div className="flex-grow border-t border-t-dark"></div>

                    <button onClick={() => switchTab("summary")} className="cursor-pointer flex flex-col justify-center items-center gap-1">
                        <span className="bg-primary w-5 h-5 rounded-full flex items-center justify-center">
                            {currentTab === "summary" && <span className="bg-white rounded-full w-2.5 h-2.5"></span>}
                        </span>
                        <span>Summary</span>
                    </button>
                </div>

                <div className="h-[50svh] overflow-y-auto scrollbar-hidden">
                    {currentTab === "details" && (
                        <RefinementDetails
                            setTab={switchTab}
                            onClose={onClose}
                            setDetailsData={setDetailsData}
                        />
                    )}
                    {currentTab === "scan" && (
                        <Scan
                            setTab={switchTab}
                            onClose={onClose}
                            setScanData={setScanData}
                        />
                    )}
                    {currentTab === "photos" && (
                        <Photos
                            setTab={switchTab}
                            handleFinalSubmit={() => switchTab("summary", true)}
                            onClose={onClose}
                            setPhotosData={setPhotosData}
                            photosData={photosData}
                            setRadiographData={setRadiographData}
                            radiographData={radiographData}
                        />
                    )}
                    {currentTab === "summary" && (
                        <Summary
                            setTab={switchTab}
                            handleFinalSubmit={handleFinalSubmit}
                            detailsData={detailsData}
                            scanData={scanData}
                            photosData={photosData}
                            radiographData={radiographData}
                        />
                    )}
                </div>
            </div>
        </div>
    );
};

export default RefinementModel;