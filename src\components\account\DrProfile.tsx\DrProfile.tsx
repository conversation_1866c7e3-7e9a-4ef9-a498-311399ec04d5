'use client';

import React, { useState } from 'react';
import { toast } from 'react-toastify';

interface Address {
    id: string;
    name: string;
    street: string;
    details: string;
    email: string;
    isDefault?: boolean;
}

const initialAddress = { name: '', street: '', details: '', email: '' };

const DrProfile = () => {
    // State for user information
    const [username, setUsername] = useState('kabrahm');
    const [password, setPassword] = useState('*************');
    const [email, setEmail] = useState('<EMAIL>');
    const [isEditingUsername, setIsEditingUsername] = useState(false);
    const [isEditingPassword, setIsEditingPassword] = useState(false);
    const [isEditingEmail, setIsEditingEmail] = useState(false);
    const [newUsername, setNewUsername] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [newEmail, setNewEmail] = useState('');

    // State for notification preferences
    const [notifications, setNotifications] = useState(true);
    const [isEditingNotifications, setIsEditingNotifications] = useState(false);

    const [shippingAddresses, setShippingAddresses] = useState<Address[]>([
        {
            id: '1',
            name: 'iBraces Dental clinic(# 1434279)',
            street: 'AlOqair street Al Hafar, 36365, SA',
            details: '+966544211711',
            email: '<EMAIL>',
            isDefault: true
        },
        {
            id: '2',
            name: 'iBraces Dental clinic(# 1434279)',
            street: 'AlOqair street Al Hafar, 36365, SA',
            details: '+966544211711',
            email: '<EMAIL>'
        }
    ]);
    const [billingAddresses, setBillingAddresses] = useState<Address[]>([
        {
            id: '1',
            name: 'iBraces Dental clinic(# 1434279)',
            street: 'AlOqair street Al Hafar, 36365, SA',
            details: '+966544211711',
            email: '<EMAIL>',
            isDefault: true
        }
    ]);

    // Shipping Modal State
    const [shippingModalOpen, setShippingModalOpen] = useState(false);
    const [shippingModalType, setShippingModalType] = useState<'add' | 'edit' | null>(null);
    const [shippingModalData, setShippingModalData] = useState<{ id?: string; name: string; street: string; details: string; email: string }>({ ...initialAddress });

    // Billing Modal State
    const [billingModalOpen, setBillingModalOpen] = useState(false);
    const [billingModalType, setBillingModalType] = useState<'add' | 'edit' | null>(null);
    const [billingModalData, setBillingModalData] = useState<{ id?: string; name: string; street: string; details: string; email: string }>({ ...initialAddress });

    // Confirmation Modal State
    const [confirmModalOpen, setConfirmModalOpen] = useState(false);
    const [addressToDelete, setAddressToDelete] = useState<{ type: 'shipping' | 'billing', id: string } | null>(null);

    // Functions to handle editing
    const handleChangeUsername = () => {
        if (isEditingUsername) {
            // Save changes
            if (newUsername.trim()) {
                setUsername(newUsername);
            }
            setIsEditingUsername(false);
        } else {
            setNewUsername(username);
            setIsEditingUsername(true);
        }
    };

    const handleChangePassword = () => {
        if (isEditingPassword) {
            // Save changes
            if (newPassword.trim()) {
                setPassword('*************'); // Always display asterisks for security
            }
            setIsEditingPassword(false);
        } else {
            setNewPassword('');
            setIsEditingPassword(true);
        }
    };

    const handleChangeEmail = () => {
        if (isEditingEmail) {
            // Save changes
            if (newEmail.trim()) {
                setEmail(newEmail);
            }
            setIsEditingEmail(false);
        } else {
            setNewEmail(email);
            setIsEditingEmail(true);
        }
    };

    const handleChangeNotifications = () => {
        if (isEditingNotifications) {
            setIsEditingNotifications(false);
        } else {
            setIsEditingNotifications(true);
        }
    };

    const toggleNotifications = () => {
        setNotifications(!notifications);
    };

    // Functions to handle address management
    const handleAddShipping = () => {
        setShippingModalType('add');
        setShippingModalData({ ...initialAddress });
        setShippingModalOpen(true);
    };
    const handleEditShipping = (address: Address) => {
        setShippingModalType('edit');
        setShippingModalData({ ...address });
        setShippingModalOpen(true);
    };
    const handleShippingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setShippingModalData({ ...shippingModalData, [e.target.name]: e.target.value });
    };
    const handleShippingSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (shippingModalType === 'add') {
            const newAddress: Address = { ...shippingModalData, id: Date.now().toString() };
            setShippingAddresses(prev => [...prev, newAddress]);
            console.log('Shipping Added:', newAddress);
            toast.success('Shipping address added successfully!');
        } else if (shippingModalType === 'edit') {
            setShippingAddresses(prev => prev.map(addr => addr.id === shippingModalData.id ? { ...shippingModalData } as Address : addr));
            console.log('Shipping Updated:', shippingModalData);
            toast.success('Shipping address updated successfully!');
        }
        setShippingModalOpen(false);
    };

    // Billing Modal Handlers
    const handleAddBilling = () => {
        setBillingModalType('add');
        setBillingModalData({ ...initialAddress });
        setBillingModalOpen(true);
    };
    const handleEditBilling = (address: Address) => {
        setBillingModalType('edit');
        setBillingModalData({ ...address });
        setBillingModalOpen(true);
    };
    const handleBillingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setBillingModalData({ ...billingModalData, [e.target.name]: e.target.value });
    };
    const handleBillingSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (billingModalType === 'add') {
            const newAddress: Address = { ...billingModalData, id: Date.now().toString() };
            setBillingAddresses(prev => [...prev, newAddress]);
            console.log('Billing Added:', newAddress);
            toast.success('Billing address added successfully!');
        } else if (billingModalType === 'edit') {
            setBillingAddresses(prev => prev.map(addr => addr.id === billingModalData.id ? { ...billingModalData } as Address : addr));
            console.log('Billing Updated:', billingModalData);
            toast.success('Billing address updated successfully!');
        }
        setBillingModalOpen(false);
    };

    // Default address handlers
    const setDefaultShippingAddress = (id: string) => {
        setShippingAddresses(shippingAddresses.map(address => ({
            ...address,
            isDefault: address.id === id
        })));
    };
    const setDefaultBillingAddress = (id: string) => {
        setBillingAddresses(billingAddresses.map(address => ({
            ...address,
            isDefault: address.id === id
        })));
    };

    // Delete handlers
    const handleDeleteAddress = (type: 'shipping' | 'billing', id: string) => {
        setAddressToDelete({ type, id });
        setConfirmModalOpen(true);
    };

    const confirmDeleteAddress = () => {
        if (!addressToDelete) return;

        const { type, id } = addressToDelete;

        if (type === 'shipping') {
            const addressToDeleteItem = shippingAddresses.find(addr => addr.id === id);
            if (addressToDeleteItem?.isDefault) {
                toast.error('Cannot delete default shipping address');
                setConfirmModalOpen(false);
                setAddressToDelete(null);
                return;
            }
            setShippingAddresses(shippingAddresses.filter(address => address.id !== id));
            toast.success('Shipping address deleted successfully');
        } else {
            const addressToDeleteItem = billingAddresses.find(addr => addr.id === id);
            if (addressToDeleteItem?.isDefault) {
                toast.error('Cannot delete default billing address');
                setConfirmModalOpen(false);
                setAddressToDelete(null);
                return;
            }
            setBillingAddresses(billingAddresses.filter(address => address.id !== id));
            toast.success('Billing address deleted successfully');
        }

        setConfirmModalOpen(false);
        setAddressToDelete(null);
    };

    const cancelDeleteAddress = () => {
        setConfirmModalOpen(false);
        setAddressToDelete(null);
    };

    const handleSaveAll = () => {
        // In a real app, you'd send all changes to the server here
        toast.success('All changes have been saved!');
    };

    return (
        <div className="bg-white p-6 rounded-lg shadow-sm">


            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    {/* User Information Section */}
                    <div className="space-y-6">
                        <h2 className="text-xl font-bold text-gray-800 mb-4">Profile</h2>
                        {/* Username */}
                        <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
                            <div>
                                <p className=" font-medium text-gray-700">Username</p>
                                {isEditingUsername ? (
                                    <input
                                        type="text"
                                        value={newUsername}
                                        onChange={(e) => setNewUsername(e.target.value)}
                                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                                    />
                                ) : (
                                    <p className=" text-gray-600">{username}</p>
                                )}
                            </div>
                            <button
                                onClick={handleChangeUsername}
                                className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors  cursor-pointer"
                            >
                                {isEditingUsername ? "Save" : "Change"}
                            </button>
                        </div>

                        {/* Password */}
                        <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
                            <div>
                                <p className="font-medium text-gray-700">Password</p>
                                {isEditingPassword ? (
                                    <input
                                        type="password"
                                        value={newPassword}
                                        onChange={(e) => setNewPassword(e.target.value)}
                                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                                    />
                                ) : (
                                    <p className="text-gray-600">{password}</p>
                                )}
                            </div>
                            <button
                                onClick={handleChangePassword}
                                className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                            >
                                {isEditingPassword ? "Save" : "Change"}
                            </button>
                        </div>

                        {/* Primary Account Email */}
                        <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
                            <div>
                                <p className="font-medium text-gray-700">Primary Account Email*</p>
                                {isEditingEmail ? (
                                    <input
                                        type="email"
                                        value={newEmail}
                                        onChange={(e) => setNewEmail(e.target.value)}
                                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                                    />
                                ) : (
                                    <p className="text-gray-600">{email}</p>
                                )}
                            </div>
                            <button
                                onClick={handleChangeEmail}
                                className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                            >
                                {isEditingEmail ? "Save" : "Change"}
                            </button>
                        </div>

                        {/* Notification Alerts */}
                        <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
                            <div>
                                <p className="font-medium text-gray-700">Notification alerts</p>
                                {isEditingNotifications && (
                                    <div className="mt-1 flex items-center space-x-2">
                                        <input
                                            type="checkbox"
                                            id="notifications"
                                            checked={notifications}
                                            onChange={toggleNotifications}
                                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                                        />
                                        <label htmlFor="notifications" className="text-gray-600">
                                            Receive email notifications
                                        </label>
                                    </div>
                                )}
                            </div>
                            <button
                                onClick={handleChangeNotifications}
                                className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                            >
                                {isEditingNotifications ? "Save" : "Change"}
                            </button>
                        </div>

                    </div>
                </div>

                <div>
                    {/* Address Management Section */}
                    <div className="space-y-6">
                        {/* Default Shipping Address */}
                        <div>
                            <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-medium text-gray-800">Default shipping address</h3>
                                <button
                                    onClick={handleAddShipping}
                                    className="bg-[#EB6309] text-white px-3 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                                >
                                    Add New
                                </button>
                            </div>

                            {/* Shipping Address List */}
                            <div className="space-y-3">
                                {shippingAddresses.map((address) => (
                                    <div key={address.id} className="border border-gray-200 rounded-md p-3 relative">
                                        <div className="flex items-start">
                                            <div className="mr-3 mt-1">
                                                <input
                                                    type="radio"
                                                    name="default-shipping"
                                                    checked={!!address.isDefault}
                                                    onChange={() => setDefaultShippingAddress(address.id)}
                                                    className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                                                />
                                            </div>
                                            <div className="w-full">
                                                <p className="font-medium">{address.name}</p>
                                                <p className="text-gray-600">{address.street}</p>
                                                <p className="text-gray-600">{address.details}</p>
                                                <p className="text-gray-600">{address.email}</p>
                                            </div>
                                            <div className="flex space-x-2 ml-2">
                                                <button
                                                    onClick={() => handleDeleteAddress('shipping', address.id)}
                                                    className="text-red-500 cursor-pointer"
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                        <path d="M10.2499 2.00006C10.1291 1.99851 10.0098 2.02617 9.90195 2.08067C9.79413 2.13517 9.70108 2.2149 9.6307 2.31309C9.56032 2.41128 9.51469 2.52501 9.49771 2.64462C9.48072 2.76422 9.49288 2.88616 9.53314 3.00006H7.32025C6.39825 3.00006 5.54317 3.45817 5.03217 4.22467L3.84857 6.00006H3.74994C3.65056 5.99866 3.55188 6.01702 3.45966 6.05408C3.36743 6.09114 3.28349 6.14616 3.21271 6.21594C3.14194 6.28572 3.08573 6.36888 3.04737 6.46057C3.00901 6.55226 2.98926 6.65067 2.98926 6.75006C2.98926 6.84945 3.00901 6.94786 3.04737 7.03955C3.08573 7.13124 3.14194 7.2144 3.21271 7.28418C3.28349 7.35396 3.36743 7.40899 3.45966 7.44605C3.55188 7.48311 3.65056 7.50147 3.74994 7.50006H20.2499C20.3493 7.50147 20.448 7.48311 20.5402 7.44605C20.6324 7.40899 20.7164 7.35396 20.7872 7.28418C20.8579 7.2144 20.9141 7.13124 20.9525 7.03955C20.9909 6.94786 21.0106 6.84945 21.0106 6.75006C21.0106 6.65067 20.9909 6.55226 20.9525 6.46057C20.9141 6.36888 20.8579 6.28572 20.7872 6.21594C20.7164 6.14616 20.6324 6.09114 20.5402 6.05408C20.448 6.01702 20.3493 5.99866 20.2499 6.00006H20.1513L18.9677 4.22467C18.4567 3.45817 17.6011 3.00006 16.6796 3.00006H14.4667C14.507 2.88616 14.5192 2.76422 14.5022 2.64462C14.4852 2.52501 14.4396 2.41128 14.3692 2.31309C14.2988 2.2149 14.2057 2.13517 14.0979 2.08067C13.9901 2.02617 13.8707 1.99851 13.7499 2.00006H10.2499ZM4.48627 9.00006L5.56244 19.043C5.71244 20.444 6.88781 21.5001 8.29681 21.5001H15.7031C17.1116 21.5001 18.2869 20.444 18.4374 19.043L19.5136 9.00006H4.48627Z" fill="#FF0000" />
                                                    </svg>
                                                </button>
                                                <button
                                                    onClick={() => handleEditShipping(address)}
                                                    className="text-orange-500 cursor-pointer"
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                        <path d="M17.3287 7.26797L18.5487 6.04797C19.8157 4.78097 19.8157 2.71897 18.5487 1.45147C17.9352 0.838465 17.1197 0.501465 16.2502 0.501465C15.3807 0.501465 14.5647 0.838965 13.9517 1.45196L12.7322 2.67147L17.3287 7.26797ZM11.6717 3.73197L2.63723 12.7665C2.44473 12.959 2.29823 13.1965 2.21323 13.454L0.538232 18.5145C0.448732 18.7835 0.519232 19.08 0.719732 19.2805C0.863232 19.4235 1.05423 19.5 1.25023 19.5C1.32923 19.5 1.40873 19.4875 1.48623 19.462L6.54523 17.7865C6.80373 17.7015 7.04173 17.555 7.23423 17.362L16.2682 8.32797L11.6717 3.73197Z" fill="#EB6309" />
                                                    </svg>
                                                    {/* <Image src={editIcon} alt="Edit" width={16} height={16} /> */}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Default Billing Address */}
                        <div>
                            <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-medium text-gray-800">Default billing address</h3>
                                <button
                                    onClick={handleAddBilling}
                                    className="bg-[#EB6309] text-white px-3 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors text-sm cursor-pointer"
                                >
                                    Add New
                                </button>
                            </div>

                            {/* Billing Address List */}
                            <div className="space-y-3">
                                {billingAddresses.map((address) => (
                                    <div key={address.id} className="border border-gray-200 rounded-md p-3 relative">
                                        <div className="flex items-start">
                                            <div className="mr-3 mt-1">
                                                <input
                                                    type="radio"
                                                    name="default-billing"
                                                    checked={!!address.isDefault}
                                                    onChange={() => setDefaultBillingAddress(address.id)}
                                                    className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                                                />
                                            </div>
                                            <div className="w-full">
                                                <p className="font-medium">{address.name}</p>
                                                <p className="text-gray-600">{address.street}</p>
                                                <p className="text-gray-600">{address.details}</p>
                                                <p className="text-gray-600">{address.email}</p>
                                            </div>
                                            <div className="flex space-x-2 ml-2">
                                                <button
                                                    onClick={() => handleDeleteAddress('billing', address.id)}
                                                    className="text-red-500 cursor-pointer"
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                        <path d="M10.2499 2.00006C10.1291 1.99851 10.0098 2.02617 9.90195 2.08067C9.79413 2.13517 9.70108 2.2149 9.6307 2.31309C9.56032 2.41128 9.51469 2.52501 9.49771 2.64462C9.48072 2.76422 9.49288 2.88616 9.53314 3.00006H7.32025C6.39825 3.00006 5.54317 3.45817 5.03217 4.22467L3.84857 6.00006H3.74994C3.65056 5.99866 3.55188 6.01702 3.45966 6.05408C3.36743 6.09114 3.28349 6.14616 3.21271 6.21594C3.14194 6.28572 3.08573 6.36888 3.04737 6.46057C3.00901 6.55226 2.98926 6.65067 2.98926 6.75006C2.98926 6.84945 3.00901 6.94786 3.04737 7.03955C3.08573 7.13124 3.14194 7.2144 3.21271 7.28418C3.28349 7.35396 3.36743 7.40899 3.45966 7.44605C3.55188 7.48311 3.65056 7.50147 3.74994 7.50006H20.2499C20.3493 7.50147 20.448 7.48311 20.5402 7.44605C20.6324 7.40899 20.7164 7.35396 20.7872 7.28418C20.8579 7.2144 20.9141 7.13124 20.9525 7.03955C20.9909 6.94786 21.0106 6.84945 21.0106 6.75006C21.0106 6.65067 20.9909 6.55226 20.9525 6.46057C20.9141 6.36888 20.8579 6.28572 20.7872 6.21594C20.7164 6.14616 20.6324 6.09114 20.5402 6.05408C20.448 6.01702 20.3493 5.99866 20.2499 6.00006H20.1513L18.9677 4.22467C18.4567 3.45817 17.6011 3.00006 16.6796 3.00006H14.4667C14.507 2.88616 14.5192 2.76422 14.5022 2.64462C14.4852 2.52501 14.4396 2.41128 14.3692 2.31309C14.2988 2.2149 14.2057 2.13517 14.0979 2.08067C13.9901 2.02617 13.8707 1.99851 13.7499 2.00006H10.2499ZM4.48627 9.00006L5.56244 19.043C5.71244 20.444 6.88781 21.5001 8.29681 21.5001H15.7031C17.1116 21.5001 18.2869 20.444 18.4374 19.043L19.5136 9.00006H4.48627Z" fill="#FF0000" />
                                                    </svg>
                                                </button>
                                                <button
                                                    onClick={() => handleEditBilling(address)}
                                                    className="text-orange-500 cursor-pointer"
                                                >
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                        <path d="M17.3287 7.26797L18.5487 6.04797C19.8157 4.78097 19.8157 2.71897 18.5487 1.45147C17.9352 0.838465 17.1197 0.501465 16.2502 0.501465C15.3807 0.501465 14.5647 0.838965 13.9517 1.45196L12.7322 2.67147L17.3287 7.26797ZM11.6717 3.73197L2.63723 12.7665C2.44473 12.959 2.29823 13.1965 2.21323 13.454L0.538232 18.5145C0.448732 18.7835 0.519232 19.08 0.719732 19.2805C0.863232 19.4235 1.05423 19.5 1.25023 19.5C1.32923 19.5 1.40873 19.4875 1.48623 19.462L6.54523 17.7865C6.80373 17.7015 7.04173 17.555 7.23423 17.362L16.2682 8.32797L11.6717 3.73197Z" fill="#EB6309" />
                                                    </svg>
                                                    {/* <Image src={editIcon} alt="Edit" width={16} height={16} /> */}
                                                </button>

                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Save Button */}
            <div className="flex justify-end mt-8">
                <button
                    onClick={handleSaveAll}
                    className="bg-[#EB6309] text-white px-6 py-2 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                >
                    Save
                </button>
            </div>
            {/* Shipping Modal */}
            {shippingModalOpen && (
                <div className="fixed inset-0 flex items-center justify-center bg-black/20 bg-opacity-30 z-50">
                    <form
                        onSubmit={handleShippingSubmit}
                        className="bg-white rounded-lg shadow-lg p-6 w-[500px] flex flex-col gap-4"
                    >
                        <h2 className="text-lg font-semibold mb-2">
                            {shippingModalType === 'add' ? 'Add New Shipping Address' : 'Update Shipping Address'}
                        </h2>
                        {shippingModalType === 'edit' && (
                            <div className="text-xs text-gray-400 mb-2">ID: {shippingModalData.id}</div>
                        )}
                        <input
                            name="name"
                            value={shippingModalData.name}
                            onChange={handleShippingChange}
                            placeholder="Clinic Name"
                            className="border border-gray-300 rounded px-3 py-2"
                            required
                        />
                        <input
                            name="street"
                            value={shippingModalData.street}
                            onChange={handleShippingChange}
                            placeholder="Street"
                            className="border border-gray-300 rounded px-3 py-2"
                            required
                        />
                        <input
                            name="details"
                            value={shippingModalData.details}
                            onChange={handleShippingChange}
                            placeholder="Details"
                            className="border border-gray-300 rounded px-3 py-2"
                            required
                        />
                        <input
                            name="email"
                            value={shippingModalData.email}
                            onChange={handleShippingChange}
                            placeholder="Email"
                            className="border border-gray-300 rounded px-3 py-2"
                            required
                        />
                        <div className="flex justify-end gap-2 mt-4">
                            <button
                                type="button"
                                className="px-4 py-2 cursor-pointer bg-gray-300 rounded hover:bg-gray-400"
                                onClick={() => setShippingModalOpen(false)}
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                className="px-4 py-2 cursor-pointer bg-[#EB6309] text-white rounded hover:bg-[#D45A08]"
                            >
                                {shippingModalType === 'add' ? 'Add' : 'Update'}
                            </button>
                        </div>
                    </form>
                </div>
            )}

            {billingModalOpen && (
                <div className="fixed inset-0 flex items-center justify-center bg-black/20 bg-opacity-30 z-50">
                    <form
                        onSubmit={handleBillingSubmit}
                        className="bg-white rounded-lg shadow-lg p-6 w-[500px] flex flex-col gap-4"
                    >
                        <h2 className="text-lg font-semibold mb-2">
                            {billingModalType === 'add' ? 'Add New Billing Address' : 'Update Billing Address'}
                        </h2>
                        {billingModalType === 'edit' && (
                            <div className="text-xs text-gray-400 mb-2">ID: {billingModalData.id}</div>
                        )}
                        <input
                            name="name"
                            value={billingModalData.name}
                            onChange={handleBillingChange}
                            placeholder="Clinic Name"
                            className="border border-gray-300 rounded px-3 py-2"
                            required
                        />
                        <input
                            name="street"
                            value={billingModalData.street}
                            onChange={handleBillingChange}
                            placeholder="Street"
                            className="border border-gray-300 rounded px-3 py-2"
                            required
                        />
                        <input
                            name="details"
                            value={billingModalData.details}
                            onChange={handleBillingChange}
                            placeholder="Details"
                            className="border border-gray-300 rounded px-3 py-2"
                            required
                        />
                        <input
                            name="email"
                            value={billingModalData.email}
                            onChange={handleBillingChange}
                            placeholder="Email"
                            className="border border-gray-300 rounded px-3 py-2"
                            required
                        />
                        <div className="flex justify-end gap-2 mt-4">
                            <button
                                type="button"
                                className="px-4 py-2 cursor-pointer bg-gray-300 rounded hover:bg-gray-400"
                                onClick={() => setBillingModalOpen(false)}
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                className="px-4 py-2 cursor-pointer bg-[#EB6309] text-white rounded hover:bg-[#D45A08]"
                            >
                                {billingModalType === 'add' ? 'Add' : 'Update'}
                            </button>
                        </div>
                    </form>
                </div>
            )}

            {/* Delete Confirmation Modal */}
            {confirmModalOpen && (
                <div className="fixed inset-0 flex items-center justify-center bg-black/20 bg-opacity-30 z-50">
                    <div className="bg-white rounded-lg shadow-lg p-6 w-[400px] flex flex-col gap-4">
                        <h2 className="text-xl font-bold text-gray-800">Confirm Deletion</h2>
                        <p className="text-gray-600">
                            Are you sure you want to delete this {addressToDelete?.type === 'shipping' ? 'shipping' : 'billing'} address?
                        </p>
                        <div className="flex justify-end gap-3 mt-4">
                            <button
                                type="button"
                                className="px-4 py-2 cursor-pointer bg-gray-300 rounded-md hover:bg-gray-400 text-gray-800"
                                onClick={cancelDeleteAddress}
                            >
                                Cancel
                            </button>
                            <button
                                type="button"
                                className="px-4 py-2 cursor-pointer bg-red-500 text-white rounded-md hover:bg-red-600"
                                onClick={confirmDeleteAddress}
                            >
                                Confirm
                            </button>
                        </div>
                    </div>
                </div>
            )}

        </div>
    );
};

export default DrProfile;