"use client";

import React, { useEffect, useState } from "react";
import { useForm, FieldErrors } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";

import FormWrapper from "../reuseable/FormWrapper";
import ScanSection from "./ScanOptions";
import PhotosSection from "./PhotosSection";
import RadiographsSection from "./RadioGraphSection";
import { StaticImport } from "next/dist/shared/lib/get-img-props";
import GeneralRecords from "./GeneralRecords";
import { submitPatientData } from "@/utils/ApisHelperFunction";
import getAndDecryptCookie from "@/app/lib/auth";

// Types
export interface DentalPhoto {
    name: string;
    file: File | null;
}
export interface Placeholder {
    name: string;
    placeholder: StaticImport;
}
const fileSchema = z.instanceof(File).nullable();

const photoSchema = z.object({
    name: z.string(),
    file: fileSchema,
});

// Zod Schema
const baseSchema = {
    scan: z
        .object({
            stlFile1: fileSchema,
            stlFile2: fileSchema,
            cbctFile: fileSchema,
        })
        .superRefine((val, ctx) => {
            if (!val.stlFile1 || !val.stlFile2) {
                ctx.addIssue({
                    path: ["stlFile1"],
                    code: z.ZodIssueCode.custom,
                    message: "Both STL scans are required",
                });
            }
        }),
    photos: z.array(photoSchema).superRefine((val, ctx) => {
        const requiredPhotos = val.filter((photo) => photo.name !== "socialSmile");
        if (requiredPhotos.some((photo) => photo.file == null)) {
            ctx.addIssue({
                path: [],
                code: z.ZodIssueCode.custom,
                message: "All photos are required",
            });
        }
    }),
    generalRecords: z.object({
        files: z.array(z.instanceof(File))
            .max(10, { message: "You can upload a maximum of 10 files" })
            .optional()
    }),
};

// Final schema with optional radiographs
const PatientRecordsSchema = z.object({
    ...baseSchema,
    radiographs: z
        .object({
            radioGraph1: fileSchema,
            radioGraph2: fileSchema,
        })
        .optional(),
});

export type ScanFormData = z.infer<typeof PatientRecordsSchema>;

const PatientRecords = () => {
    const router = useRouter();
    const [treatmentOption, setTreatmentOption] = useState<string | null>(null);

    useEffect(() => {
        const option = localStorage.getItem("treatmentOption");
        setTreatmentOption(option);
    }, []);
    console.log(treatmentOption)

    const {
        register,
        watch,
        setValue,
        handleSubmit,
        formState: { errors },
    } = useForm<ScanFormData>({
        resolver: zodResolver(PatientRecordsSchema),
        defaultValues: {
            scan: {
                stlFile1: null,
                stlFile2: null,
                cbctFile: null,
            },
            photos: [],
            radiographs: {
                radioGraph1: null,
                radioGraph2: null,
            },
            generalRecords: {
                files: []
            }
        },
    });

    const data = watch();

    useEffect(() => {
        console.log("DATA====>", data);
    }, [data]);

    const onSubmit = async (data: ScanFormData) => {
        console.log("🚀 ~ onSubmit ~ data:", data)
        try {
            const token = getAndDecryptCookie("AccessToken");
            const patientId = getAndDecryptCookie("patientId");

            if (!token || !patientId) {
                console.error("Token or Patient ID is missing");
                toast.error("Authentication required.");
                return;
            }

            // Safe way to extract files with proper null checks - FIXED
            const profileRepose = data.photos.find((photo) => photo.name === "profileRepose")?.file || null;
            const buccalRight = data.photos.find((photo) => photo.name === "buccalRight")?.file || null;
            const buccalLeft = data.photos.find((photo) => photo.name === "buccalLeft")?.file || null;
            const frontalRepose = data.photos.find((photo) => photo.name === "frontalRepose")?.file || null;
            const frontalSmiling = data.photos.find((photo) => photo.name === "frontalSmiling")?.file || null;
            const labialAnterior = data.photos.find((photo) => photo.name === "labialAnterior")?.file || null;
            const occlussalLower = data.photos.find((photo) => photo.name === "occlussalLower")?.file || null;
            const occlussalUpper = data.photos.find((photo) => photo.name === "occlussalUpper")?.file || null;

            // Validate that all required files are present
            if (!data.scan.stlFile1 || !data.scan.stlFile2 || !data.scan.cbctFile ||
                !profileRepose || !buccalRight || !buccalLeft || !frontalRepose || 
                !frontalSmiling || !labialAnterior || !occlussalLower || !occlussalUpper) {
                toast.error("All required files must be uploaded.");
                return;
            }

            const records = {
                stlFile1: data.scan.stlFile1,
                stlFile2: data.scan.stlFile2,
                cbctFile: data.scan.cbctFile,
                profileRepose: profileRepose,
                buccalRight: buccalRight,
                buccalLeft: buccalLeft,
                frontalRepose: frontalRepose,
                frontalSmiling: frontalSmiling,
                labialAnterior: labialAnterior,
                occlussalLower: occlussalLower,
                occlussalUpper: occlussalUpper,
                radioGraph1: data.radiographs?.radioGraph1 || null,
                radioGraph2: data.radiographs?.radioGraph2 || null,
                generalRecords: data.generalRecords?.files || []
            };

            // Call the helper function
            const response = await submitPatientData(token, patientId, records);

            if (response) {
                console.log("Patient data submitted successfully:", response);
                toast.success("Patient data submitted successfully.");
                router.push("/case-prescription"); 
            } else {
                console.error("Failed to submit patient data");
                toast.error("Failed to submit patient data.");
            }
        } catch (error) {
            console.error("Error during patient data submission:", error);
            toast.error("An error occurred while submitting patient data.");
        }
    };

    const onError = (errors: FieldErrors<ScanFormData>) => {
        toast.error("Please fill all the required fields.");
        console.log("❌ Form validation errors:", errors);
    };

    return (
        <FormWrapper
            classNames="!grid-cols-1"
            onSubmit={handleSubmit(onSubmit, onError)}
            onBack={() => { }}
        >
            <div className="flex flex-col gap-6">
                <PhotosSection
                    register={register}
                    watch={watch}
                    setValue={setValue}
                    errors={errors}
                />
                <RadiographsSection
                    register={register}
                    watch={watch}
                    setValue={setValue}
                    errors={errors}
                />
                <ScanSection
                    register={register}
                    watch={watch}
                    setValue={setValue}
                    errors={errors}
                />
                <GeneralRecords
                    register={register}
                    watch={watch}
                    setValue={setValue}
                    errors={errors}
                />
            </div>
        </FormWrapper>
    );
};

export default PatientRecords;
