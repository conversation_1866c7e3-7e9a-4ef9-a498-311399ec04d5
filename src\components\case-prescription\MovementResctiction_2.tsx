'use client'
import { useCallback, useEffect } from "react";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import { DefaultTransition } from "./ArchToTreat_1";
import { FieldErrors, FieldValues, Path, PathValue, UseFormRegister, UseFormSetValue, UseFormWatch } from "react-hook-form";
import StepsCheckBoxRegister from "../reuseable/SquareCheckBoxRegister";
import { AnimatePresence, motion } from "framer-motion";




const upperTeeth = [
    '1.8', '1.7', '1.6', '1.5', '1.4', '1.3', '1.2', '1.1',
    '2.1', '2.2', '2.3', '2.4', '2.5', '2.6', '2.7', '2.8',
];
const lowerTeeth = [
    '4.8', '4.7', '4.6', '4.5', '4.4', '4.3', '4.2', '4.1',
    '3.1', '3.2', '3.3', '3.4', '3.5', '3.6', '3.7', '3.8'
];


const upperTeethDefination = [
    '5.5', '5.4', '5.3', '5.2', '5.1',
    '6.1', '6.2', '6.3', '6.4', '6.5'
];

const lowerTeethDefination = [
    '8.5', '8.4', '8.3', '8.2', '8.1',
    '7.1', '7.2', '7.3', '7.4', '7.5'
];


export interface SpecialProps<T extends FieldValues> {
    register: UseFormRegister<T>;
    watch: UseFormWatch<T>;
    errors: FieldErrors<T>;
    setValue: UseFormSetValue<T>;
    number: string;
} 

const MovementResctiction_2 = <T extends FieldValues>({ register, errors, watch, setValue, number }: SpecialProps<T>) => {
    const movementResctriction = watch("movementResctriction.option" as Path<T>)
    const movementResctrictionTeeth = watch("movementResctriction.restrictedTeeth"  as Path<T>)
    const movementResctrictionPrimaryTeeth = watch("movementResctriction.primaryDefinationTeeth"  as Path<T>)
    const missingTeeth = watch("teethInformation.missingTeeth"  as Path<T>)
    const primaryAllowedTeeth = watch("teethInformation.primaryDefinationTeeth"  as Path<T>)
    const handleOptionChange = useCallback(() => {
        if (movementResctriction === "none") {
            setValue("movementResctriction.restrictedTeeth"  as Path<T>, [] as PathValue<T, Path<T>>)
            setValue("movementResctriction.primaryDefinationTeeth"  as Path<T>, [] as PathValue<T, Path<T>>)
        }
    }, [movementResctriction, setValue]);

    const updateTeeth = useCallback(() => {
        if (!Array.isArray(missingTeeth) || missingTeeth.length === 0) return;
        const updated = movementResctrictionTeeth.filter((tooth: string) => {
            return !missingTeeth.includes(tooth)
        });
        // Only set if changed
        const isSame = updated.length === movementResctrictionTeeth.length &&
            updated.every((v: string, i: number) => v === movementResctrictionTeeth[i]);
        if (!isSame) {
            setValue("movementResctriction.restrictedTeeth" as Path<T>, updated);
        }
    }, [missingTeeth, movementResctrictionTeeth, setValue]);

    // Memoize the primary teeth update function
    const updateTeethPrimary = useCallback(() => {
        if (!Array.isArray(primaryAllowedTeeth) || primaryAllowedTeeth.length === 0) return;
        if (primaryAllowedTeeth.length > 0) {
            const updated = movementResctrictionPrimaryTeeth.filter((tooth: string) => {
                return primaryAllowedTeeth.includes(tooth)
            });
            // Only set if changed
            const isSame = updated.length === movementResctrictionPrimaryTeeth.length &&
                updated.every((v: string, i: number) => v === movementResctrictionPrimaryTeeth[i]);
            if (!isSame) {
                setValue("movementResctriction.primaryDefinationTeeth" as Path<T>, updated);
            }
        }
    }, [primaryAllowedTeeth, movementResctrictionPrimaryTeeth, setValue]);
    // const updateTeethPrimary = useCallback(() => {
    //     if (!Array.isArray(primaryAllowedTeeth) || primaryAllowedTeeth.length === 0) return;
    //     if (primaryAllowedTeeth.length > 0) {
    //         const updated = movementResctrictionPrimaryTeeth.filter((tooth: string) => {
    //             return primaryAllowedTeeth.includes(tooth)
    //         });
    //         setValue("movementResctriction.primaryDefinationTeeth", updated);
    //         console.log("🚀 ~ 3 ~ updated:", updated)
    //     }
    // }, [primaryAllowedTeeth, movementResctrictionPrimaryTeeth, setValue]);

    useEffect(() => {
        handleOptionChange();
    }, [handleOptionChange]);

    useEffect(() => {
        updateTeeth();
    }, [updateTeeth]);

    useEffect(() => {
        updateTeethPrimary();
    }, [primaryAllowedTeeth, movementResctrictionPrimaryTeeth, setValue]);


    return (
        <div>
            <h3 className="font-bold text-lg  text-dark mb-2">{`${number}`}{" "}Tooth movement restrictions <span className='text-sm'>{"(ex. bridges, ankylosed teeth, implants, etc.)"}</span></h3>
            <div className="space-y-2 text-sm text-gray-700">
                <div className="flex items-center gap-2">
                    <label className="flex items-center gap-2">
                        <RoundRadioButton
                            id="movementRestrictionNone"
                            label="None (move all teeth)"
                            value="none"
                            register={register}
                            name="movementResctriction.option"
                            labelClass='!text-[#434343] text-base'
                        />
                    </label>
                    <label className="flex items-start gap-2">
                        <RoundRadioButton
                            id="movementRestrictionSpecific"
                            label="These specific teeth should not be moved"
                            value="specific"
                            register={register}
                            name="movementResctriction.option"
                            labelClass='!text-[#434343] text-base'

                        />
                    </label>
                </div>

                <AnimatePresence initial={false} mode="wait">
                    {movementResctriction === 'specific' && (
                        <motion.div
                            key="upper"
                            initial={{ height: 0, opacity: 0 }}
                            animate={{ height: 'auto', opacity: 1 }}
                            exit={{ height: 0, opacity: 0 }}
                            transition={DefaultTransition}
                            style={{ overflow: 'hidden' }}
                        >
                            <div className="sm:px-10 px-5">
                                <div>
                                    <div className="flex items-center gap-4">
                                        <div className='font-bold text-xl text-dark'>R</div>
                                        <div className='flex flex-col gap-2'>
                                            <div className='flex gap-2 items-center'>
                                                {upperTeeth.map((teeth: string, index: number) => {
                                                    const disabled = missingTeeth.includes(teeth)
                                                    return <StepsCheckBoxRegister register={register("movementResctriction.restrictedTeeth"  as Path<T>)} disabled={disabled} disabledClass="!bg-dark" key={index} label={teeth} id={`restriction-${teeth}-${index}`} value={teeth} />
                                                })}
                                            </div>

                                            <div className='border-t border-t-gray'></div>

                                            <div className='flex gap-2 items-center'>
                                                {lowerTeeth.map((teeth: string, index: number) => {
                                                    const disabled = missingTeeth.includes(teeth)
                                                    return <StepsCheckBoxRegister register={register("movementResctriction.restrictedTeeth"  as Path<T>)} disabled={disabled} disabledClass="!bg-dark" key={index} label={teeth} id={`restriction-${teeth}-${index}`} value={teeth} rootLableClassName='!flex-col' />
                                                })}
                                            </div>
                                        </div>
                                        <div className='font-bold text-xl text-dark'>L</div>
                                    </div>
                                </div>

                                <p className="mb-7 mt-3">Note: Black represents missing tooth, Red represents movement restricted tooth</p>
                                {Array.isArray(primaryAllowedTeeth) && primaryAllowedTeeth.length > 0 && (
                                    <div>
                                        <div>
                                            <div className="flex items-center gap-4">
                                                <div className='font-bold text-xl text-dark'>R</div>
                                                <div className='flex flex-col gap-2'>
                                                    <div className='flex gap-8 items-center'>
                                                        {upperTeethDefination.map((teeth: string, index: number) => {
                                                            const disabled: boolean = primaryAllowedTeeth.includes(teeth);
                                                            return (
                                                                <StepsCheckBoxRegister
                                                                    register={register("movementResctriction.primaryDefinationTeeth" as Path<T>)}
                                                                    disabled={!disabled}
                                                                    fadeOnDisable={true}
                                                                    key={index}
                                                                    label={teeth}
                                                                    id={`movement-primary-${teeth}-${index}`}
                                                                    value={teeth}
                                                                    className={"!bg-gray-600 peer-checked:!bg-primary peer-checked:!border-transparent"}
                                                                />
                                                            );
                                                        })}
                                                    </div>

                                                    <div className='border-t border-t-gray'></div>

                                                    <div className='flex gap-8 items-center'>
                                                        {lowerTeethDefination.map((teeth: string, index: number) => {
                                                            const disabled: boolean = primaryAllowedTeeth.includes(teeth);
                                                            return (
                                                                <StepsCheckBoxRegister
                                                                    register={register("movementResctriction.primaryDefinationTeeth" as Path<T>)}
                                                                    className={"!bg-gray-600 peer-checked:!bg-primary peer-checked:!border-transparent"}
                                                                    disabled={!disabled}
                                                                    fadeOnDisable={true}
                                                                    key={index}
                                                                    label={teeth}
                                                                    id={`movement-primary-${teeth}-${index}`}
                                                                    value={teeth}
                                                                    rootLableClassName='!flex-col'
                                                                />
                                                            );
                                                        })}
                                                    </div>
                                                </div>
                                                <div className='font-bold text-xl text-dark'>L</div>
                                            </div>
                                        </div>
                                        <p className="mb-7 mt-3">Note: Click the primary dentition you do not want any attachment on.</p>
                                    </div>
                                )}
                            </div>
                            {"restrictedTeeth" in (errors.movementResctriction ?? {}) &&
                                (errors.movementResctriction as FieldErrors<T>)?.restrictedTeeth && (
                                    <p className="text-red-500 text-sm mt-1">
                                        {(errors.movementResctriction as FieldErrors<T>)?.restrictedTeeth?.message as string}
                                    </p>
                                )}
                        </motion.div>
                    )}


                </AnimatePresence>
            </div>

        </div>
    )
}

export default MovementResctiction_2