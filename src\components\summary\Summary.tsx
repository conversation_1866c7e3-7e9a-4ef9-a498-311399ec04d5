'use client';

import React, { useEffect, useState } from 'react';
import UploadedRecords from '../reuseable/UploadedRecords';
import SummaryCard from './SummaryCard';
import SummaryCardNumbered from './SummaryCardNumbered';
import { capitalizeFirstWord } from '@/utils/helperFunctions';
import { PrescriptionSchemaType } from '../case-prescription/versions/LongVersion';
import { useRouter } from 'next/navigation';
import getAndDecryptCookie from '@/app/lib/auth';

const toRegularCase = (str: string | string[] | undefined) => {
  if (!str) return '';
  if (Array.isArray(str)) {
    return str.join(', ');
  }
  return str
    .replace(/([A-Z])/g, ' $1')   // insert space before capital letters
    .replace(/^./, (char) => char.toUpperCase()); // capitalize first letter
};

// Helper function to check if section has data
interface HasDataFn {
  (condition: unknown): boolean;
}

const hasData: HasDataFn = (condition) => {
  if (condition === undefined || condition === null) return false;
  if (typeof condition === 'string') return condition.trim() !== '';
  if (typeof condition === 'boolean') return true;
  if (Array.isArray(condition)) return condition.length > 0;
  if (typeof condition === 'object') return Object.keys(condition).length > 0;
  return true;
};

type PatientData = {
  firstName: string;
  lastName: string;
  gender: string;
  birthDay: number;
  birthMonth: number;
  birthyear: number;
  billAddress: string;
  shipAddress: string;
};

const getKeysWithValues = (data: PrescriptionSchemaType["spacing"]["resolveUpper"] | PrescriptionSchemaType["spacing"]["resolveLower"] | undefined) => {
  if (!data) return null;

  return Object.entries(data).map(([key, value], index) => (
    <div key={index} className='text-dark text-sm flex items-center gap-6'>
      <div className='min-w-[15%] max-w-[15%]'>{toRegularCase(key)}</div>
      <div>{toRegularCase(value || '')}</div>
    </div>
  ));
};

const MainSummary = () => {
  const [patientData, setPatientData] = useState<PatientData>({
    firstName: "",
    lastName: "",
    gender: "",
    birthDay: 1,
    birthMonth: 1,
    birthyear: 1,
    billAddress: "",
    shipAddress: "",
  });

  const [casePrescriptionData, setCasePrescriptionData] = useState<PrescriptionSchemaType>();
  console.log(casePrescriptionData);
  const patientId = getAndDecryptCookie("patientId");
  useEffect(() => {
    try {
      const longdata = localStorage.getItem("casePrescriptionData");
      const shortdata = localStorage.getItem("shortCasePrescriptionData");
      if (shortdata) {
        setCasePrescriptionData(JSON.parse(shortdata));
      } else {
        if (longdata) {
          setCasePrescriptionData(JSON.parse(longdata));
        } else {
          setCasePrescriptionData(undefined);
        }
      }
      const patientDataLocal = localStorage.getItem("patientData");
      if (patientDataLocal) {
        setPatientData(JSON.parse(patientDataLocal));
      }
    } catch (error) {
      console.log("Error loading localStorage data:", error);
    }
  }, []);

  const router = useRouter();

  // Create section definitions with data and visibility conditions
  const sections = [
    {
      title: "Arch to treat",
      href: "/case-prescription",
      hasData: hasData(casePrescriptionData?.archToTreat?.option),
      content: (
        <div className='flex flex-col gap-2'>
          <div className='text-sm'>{toRegularCase(casePrescriptionData?.archToTreat.option || "")}</div>
          {casePrescriptionData?.archToTreat.option !== "both" && (
            <div className='ps-6 text-sm'>
              <span className='font-semibold'></span> {casePrescriptionData?.archToTreat.value}
            </div>
          )}
        </div>
      )
    },
    {
      title: "Tooth information",
      href: "/case-prescription",
      hasData: casePrescriptionData?.teethInformation?.missingTeethOption === "missingTeeth" || 
               casePrescriptionData?.teethInformation?.primaryDefinationOption === "primaryDefinition",
      content: (
        <div>
          <div className='mb-1 text-sm'>
            <span className='font-semibold'>Missing Teeth:</span>{" "}
            {casePrescriptionData?.teethInformation.missingTeethOption === "missingTeeth" ? "Yes" : "None"}
          </div>

          {casePrescriptionData?.teethInformation.missingTeethOption === "missingTeeth" && (
            <div className='flex items-center gap-2 mb-4'>
              {casePrescriptionData.teethInformation.missingTeeth.map((teeth: string, index: number) => (
                <span className='bg-orange text-white p-1 rounded-sm text-sm' key={index}>{teeth}</span>
              ))}
            </div>
          )}

          <div className='mb-1 text-sm'>
            <span className='font-semibold'>Primary Dentition:</span>{" "}
            {casePrescriptionData?.teethInformation.primaryDefinationOption === "primaryDefinition" ? "Yes" : "None"}
          </div>

          {casePrescriptionData?.teethInformation.primaryDefinationOption === "primaryDefinition" && (
            <div className='flex items-center gap-2'>
              {casePrescriptionData.teethInformation.primaryDefinationTeeth.map((teeth: string, index: number) => (
                <span className='bg-orange text-white p-1 rounded-sm text-sm' key={index}>{teeth}</span>
              ))}
            </div>
          )}
        </div>
      )
    },
    {
      title: "Tooth movement restrictions",
      sub: "(ex. bridges, ankylosed teeth, implants, etc.)",
      href: "/case-prescription",
      hasData: hasData(casePrescriptionData?.movementResctriction?.option),
      content: (
        <div>
          <div className='mb-1 text-sm'>
            <span className='font-semibold'>Teeth:</span>{" "}
            {casePrescriptionData?.movementResctriction.option === "specific"
              ? "Avoid these teeth"
              : "None (Move all teeth)"}
          </div>

          {casePrescriptionData?.movementResctriction.option === "specific" && (
            <div className='flex items-center gap-2 mb-5'>
              {casePrescriptionData.movementResctriction.restrictedTeeth.map((teeth: string, index: number) => (
                <span className='bg-orange text-white p-1 rounded-sm text-sm' key={index}>{teeth}</span>
              ))}
            </div>
          )}

          {casePrescriptionData?.movementResctriction.option === "specific" && 
           casePrescriptionData.movementResctriction.primaryDefinationTeeth &&
           casePrescriptionData.movementResctriction.primaryDefinationTeeth.length > 0 && (
            <div>
              <div className='mb-1 text-sm'>
                <span className='font-semibold'>Primary Definition:</span>
              </div>
              <div className='flex items-center gap-2'>
                {casePrescriptionData.movementResctriction.primaryDefinationTeeth.map((teeth: string, index: number) => (
                  <span className='bg-orange text-white p-1 rounded-sm text-sm' key={index}>{teeth}</span>
                ))}
              </div>
            </div>
          )}
        </div>
      )
    },
    {
      title: "Attachments",
      sub: "(To specify attachments, see Clinical Preferences)",
      href: "/case-prescription",
      hasData: hasData(casePrescriptionData?.attachmentRestrictionSchema?.option),
      content: (
        <div>
          <div className='mb-1 text-sm'>
            <span className='font-semibold'>Teeth:</span>{" "}
            {casePrescriptionData?.attachmentRestrictionSchema.option === "specific"
              ? "Avoid these teeth"
              : "None"}
          </div>

          {casePrescriptionData?.attachmentRestrictionSchema.option === "specific" && (
            <div className='flex items-center gap-2 mb-5'>
              {casePrescriptionData.attachmentRestrictionSchema.restrictedTeeth.map((teeth: string, index: number) => (
                <span className='bg-orange text-white p-1 rounded-sm text-sm' key={index}>{teeth}</span>
              ))}
            </div>
          )}

          {casePrescriptionData?.attachmentRestrictionSchema.option === "specific" && 
           casePrescriptionData.attachmentRestrictionSchema.primaryDefinationTeeth &&
           casePrescriptionData.attachmentRestrictionSchema.primaryDefinationTeeth.length > 0 && (
            <div>
              <div className='mb-1 text-sm'>
                <span className='font-semibold'>Primary Definition:</span>
              </div>
              <div className='flex items-center gap-2'>
                {casePrescriptionData.attachmentRestrictionSchema.primaryDefinationTeeth.map((teeth: string, index: number) => (
                  <span className='bg-orange text-white p-1 rounded-sm text-sm' key={index}>{teeth}</span>
                ))}
              </div>
            </div>
          )}
        </div>
      )
    },
    {
      title: "Sagittal Relationship",
      href: "/case-prescription",
      hasData: hasData(casePrescriptionData?.sagitalrRelationShip?.right?.improveCanine) || 
               hasData(casePrescriptionData?.sagitalrRelationShip?.right?.improveMolar) ||
               hasData(casePrescriptionData?.sagitalrRelationShip?.left?.improveCanine) ||
               hasData(casePrescriptionData?.sagitalrRelationShip?.left?.improveMolar),
      content: (
        <div className='flex flex-col gap-3'>
          {casePrescriptionData?.sagitalrRelationShip?.right?.improveCanine && (
            <div>
              <div><span className="font-semibold">Improve Canine (Right):</span> {casePrescriptionData.sagitalrRelationShip.right.improveCanine == "on" ? "Yes" : "No"}</div>
              {casePrescriptionData.sagitalrRelationShip.right.improveCanineOption && (
                <div><span className="font-semibold">Improve Canine Option:</span> {casePrescriptionData.sagitalrRelationShip.right.improveCanineOption}</div>
              )}
              {casePrescriptionData.sagitalrRelationShip.right.improveCanineNote && (
                <div><span className="font-semibold">Improve Canine Note:</span> {casePrescriptionData.sagitalrRelationShip.right.improveCanineNote}</div>
              )}
            </div>
          )}

          {casePrescriptionData?.sagitalrRelationShip?.right?.improveMolar && (
            <div>
              <div><span className="font-semibold">Improve Molar (Right):</span> {String(casePrescriptionData.sagitalrRelationShip.right.improveMolar) == "on" ? "Yes" : "No"}</div>
              {casePrescriptionData.sagitalrRelationShip.right.improveMolarOption && (
                <div><span className="font-semibold">Improve Molar Option:</span> {casePrescriptionData.sagitalrRelationShip.right.improveMolarOption}</div>
              )}
              {casePrescriptionData.sagitalrRelationShip.right.improveMolarNote && (
                <div><span className="font-semibold">Improve Molar Note:</span> {casePrescriptionData.sagitalrRelationShip.right.improveMolarNote}</div>
              )}
            </div>
          )}

          {casePrescriptionData?.sagitalrRelationShip?.left?.improveCanine && (
            <div>
              <div><span className="font-semibold">Improve Canine (Left):</span> {String(casePrescriptionData.sagitalrRelationShip.left.improveCanine) == "on" ? "Yes" : "No"}</div>
              {casePrescriptionData.sagitalrRelationShip.left.improveCanineOption && (
                <div><span className="font-semibold">Improve Canine Option:</span> {casePrescriptionData.sagitalrRelationShip.left.improveCanineOption}</div>
              )}
              {casePrescriptionData.sagitalrRelationShip.left.improveCanineNote && (
                <div><span className="font-semibold">Improve Canine Note:</span> {casePrescriptionData.sagitalrRelationShip.left.improveCanineNote}</div>
              )}
            </div>
          )}

          {casePrescriptionData?.sagitalrRelationShip?.left?.improveMolar && (
            <div>
              <div><span className="font-semibold">Improve Molar (Left):</span> {String(casePrescriptionData.sagitalrRelationShip.left.improveMolar) == "on" ? "Yes" : "No"}</div>
              {casePrescriptionData.sagitalrRelationShip.left.improveMolarOption && (
                <div><span className="font-semibold">Improve Molar Option:</span> {casePrescriptionData.sagitalrRelationShip.left.improveMolarOption}</div>
              )}
              {casePrescriptionData.sagitalrRelationShip.left.improveMolarNote && (
                <div><span className="font-semibold">Improve Molar Note:</span> {casePrescriptionData.sagitalrRelationShip.left.improveMolarNote}</div>
              )}
            </div>
          )}
        </div>
      )
    },
    {
      title: "Overjet",
      href: "/case-prescription",
      hasData: hasData(casePrescriptionData?.overjet?.option),
      content: (
        <div>
          <div>{toRegularCase(casePrescriptionData?.overjet?.option || "")}</div>
        </div>
      )
    },
    {
      title: "Overbite",
      href: "/case-prescription",
      hasData: hasData(casePrescriptionData?.overbite?.option),
      content: (
        <div>
          {casePrescriptionData?.overbite?.option && (
            <div className="mb-1">
              {toRegularCase(casePrescriptionData.overbite.option || "")}
            </div>
          )}

          {(casePrescriptionData?.overbite?.upper &&
            (
              (Array.isArray(casePrescriptionData.overbite.upper.options) && casePrescriptionData.overbite.upper.options.length > 0) ||
              casePrescriptionData.overbite.upper.others ||
              (Array.isArray(casePrescriptionData.overbite.upper.anteriorBiteRamps) && casePrescriptionData.overbite.upper.anteriorBiteRamps.length > 0) ||
              casePrescriptionData.overbite.upper.otherNote
            )) && (
              <div className="mb-2">
                <div className="font-semibold">Upper:</div>
                {Array.isArray(casePrescriptionData.overbite.upper.options) && casePrescriptionData.overbite.upper.options.length > 0 && (
                  <div><span className="font-semibold">Options:</span> {casePrescriptionData.overbite.upper.options.join(", ")}</div>
                )}
                {casePrescriptionData.overbite.upper.others && (
                  <div><span className="font-semibold">Others:</span> Yes</div>
                )}
                {casePrescriptionData.overbite.upper.otherNote && (
                  <div><span className="font-semibold">Other Note:</span> {casePrescriptionData.overbite.upper.otherNote}</div>
                )}
                {Array.isArray(casePrescriptionData.overbite.upper.anteriorBiteRamps) && casePrescriptionData.overbite.upper.anteriorBiteRamps.length > 0 && (
                  <div><span className="font-semibold">Anterior Bite Ramps:</span> {casePrescriptionData.overbite.upper.anteriorBiteRamps.join(", ")}</div>
                )}
              </div>
            )}

          {(casePrescriptionData?.overbite?.lower &&
            (
              (Array.isArray(casePrescriptionData.overbite.lower.options) && casePrescriptionData.overbite.lower.options.length > 0) ||
              casePrescriptionData.overbite.lower.others ||
              (Array.isArray(casePrescriptionData.overbite.lower.anteriorBiteRamps) && casePrescriptionData.overbite.lower.anteriorBiteRamps.length > 0) ||
              casePrescriptionData.overbite.lower.otherNote
            )) && (
              <div className="mb-2">
                <div className="font-semibold">Lower:</div>
                {Array.isArray(casePrescriptionData.overbite.lower.options) && casePrescriptionData.overbite.lower.options.length > 0 && (
                  <div><span className="font-semibold">Options:</span> {casePrescriptionData.overbite.lower.options.join(", ")}</div>
                )}
                {casePrescriptionData.overbite.lower.others && (
                  <div><span className="font-semibold">Others:</span> Yes</div>
                )}
                {casePrescriptionData.overbite.lower.otherNote && (
                  <div><span className="font-semibold">Other Note:</span> {casePrescriptionData.overbite.lower.otherNote}</div>
                )}
                {Array.isArray(casePrescriptionData.overbite.lower.anteriorBiteRamps) && casePrescriptionData.overbite.lower.anteriorBiteRamps.length > 0 && (
                  <div><span className="font-semibold">Anterior Bite Ramps:</span> {casePrescriptionData.overbite.lower.anteriorBiteRamps.join(", ")}</div>
                )}
              </div>
            )}
        </div>
      )
    },
    {
      title: "Anterior Crossbite",
      href: "/case-prescription",
      hasData: hasData(casePrescriptionData?.anteriorCrossBite?.option),
      content: (
        <div>
          <div className='mb-1'>{toRegularCase(casePrescriptionData?.anteriorCrossBite?.option || "")}</div>
          {(casePrescriptionData?.anteriorCrossBite?.option == "correct" || casePrescriptionData?.anteriorCrossBite?.option == "improve") &&
            Array.isArray(casePrescriptionData?.anteriorCrossBite?.location) && 
            casePrescriptionData.anteriorCrossBite.location.length > 0 && (
              <div className='flex flex-col'>
                <div>
                  <span className="font-semibold">Location: </span>
                  {casePrescriptionData.anteriorCrossBite.location.map((loc: string, idx: number) => (
                    <span key={idx} className="inline-block mr-2">{toRegularCase(loc)}</span>
                  ))}
                </div>
              </div>
            )
          }
        </div>
      )
    },
    {
      title: "Posterior Crossbite",
      href: "/case-prescription",
      hasData: hasData(casePrescriptionData?.posteriorCrossBite?.option),
      content: (
        <div>
          <div className='mb-1'>{toRegularCase(casePrescriptionData?.posteriorCrossBite?.option || "")}</div>
          {(casePrescriptionData?.posteriorCrossBite?.option == "correct") &&
            Array.isArray(casePrescriptionData?.posteriorCrossBite?.location) && 
            casePrescriptionData.posteriorCrossBite.location.length > 0 && (
              <div className='flex flex-col'>
                <div>
                  <span className="font-semibold">Location: </span>
                  {casePrescriptionData.posteriorCrossBite.location.map((loc: string, idx: number) => (
                    <span key={idx} className="inline-block mr-2">{toRegularCase(loc)}</span>
                  ))}
                </div>
              </div>
            )
          }
        </div>
      )
    },
    {
      title: "Midline",
      href: "/case-prescription",
      hasData: hasData(casePrescriptionData?.midline?.option),
      content: (
        <div>
          {casePrescriptionData?.midline?.option !== "improveMidlineWithIPR" ? (
            <div>{toRegularCase(casePrescriptionData?.midline?.option || "")}</div>
          ) : (
            <div className="flex flex-col gap-2">
              {casePrescriptionData?.midline?.midlineIPRUpper && (
                <div>
                  <div className="mb-1 font-semibold">Upper</div>
                  <div>{toRegularCase(casePrescriptionData.midline.upperMidlineShift || "")}</div>
                </div>
              )}
              {casePrescriptionData.midline.midlineIPRLower && (
                <div>
                  <div className="mb-1 font-semibold">Lower</div>
                  <div>{toRegularCase(casePrescriptionData.midline.lowerMidlineShift || "")}</div>
                </div>
              )}
            </div>
          )}
        </div>
      )
    },
    {
      title: "Spacing & Crowding (Arch Length Discrepancy)",
      href: "/case-prescription",
      hasData: hasData(casePrescriptionData?.spacing?.option) || 
               hasData(casePrescriptionData?.spacing?.resolveUpper) || 
               hasData(casePrescriptionData?.spacing?.resolveLower),
      content: (
        <div className="mt-1 flex flex-col gap-2">
          {/* Spacing Option */}
          {casePrescriptionData?.spacing?.option && (
            <div className="flex flex-col gap-1">
              <p className="font-semibold text-sm">Spacing</p>
              <div className="font-normal text-sm">
                {toRegularCase(casePrescriptionData.spacing.option)}
              </div>
            </div>
          )}

          {/* Crowding Section */}
          {(hasData(casePrescriptionData?.spacing?.resolveUpper) || hasData(casePrescriptionData?.spacing?.resolveLower)) && (
            <div className="flex flex-col gap-1">
              <p className="font-semibold text-sm">Crowding</p>

              {/* Resolve Upper */}
              {hasData(casePrescriptionData?.spacing?.resolveUpper) && (
                <>
                  <p className="font-semibold text-xsm my-1">Resolve Upper</p>
                  <div className="flex flex-col gap-1">
                    {getKeysWithValues({
                      expand: casePrescriptionData?.spacing?.resolveUpper?.expand || undefined,
                      procline: casePrescriptionData?.spacing?.resolveUpper?.procline || undefined,
                      iprAnterior: casePrescriptionData?.spacing?.resolveUpper?.iprAnterior || undefined,
                      molarDistalization: casePrescriptionData?.spacing?.resolveUpper?.molarDistalization || undefined,
                    })}
                  </div>
                </>
              )}

              {/* Resolve Lower */}
              {hasData(casePrescriptionData?.spacing?.resolveLower) && (
                <>
                  <p className="font-semibold text-xsm my-1">Resolve Lower</p>
                  <div className="flex flex-col gap-1">
                    {getKeysWithValues({
                      expand: casePrescriptionData?.spacing?.resolveLower?.expand || undefined,
                      procline: casePrescriptionData?.spacing?.resolveLower?.procline || undefined,
                      iprAnterior: casePrescriptionData?.spacing?.resolveLower?.iprAnterior || undefined,
                      molarDistalization: casePrescriptionData?.spacing?.resolveLower?.molarDistalization || undefined,
                    })}
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      )
    },
    {
      title: "Extractions",
      href: "/case-prescription",
      hasData: hasData(casePrescriptionData?.extraction?.option),
      content: (
        <div>
          <div className="mb-1">
            Teeth: <strong>{casePrescriptionData?.extraction?.option === "extraction" ? "Extract these teeth:" : "None"}</strong>
          </div>
          {casePrescriptionData?.extraction?.option === "extraction" && (
            <>
              {casePrescriptionData.extraction.extractionTeeth && casePrescriptionData.extraction.extractionTeeth.length > 0 && (
                <div className="flex items-center gap-2 mb-5">
                  {casePrescriptionData.extraction.extractionTeeth.map((teeth: string, index: number) => (
                    <span className="bg-orange text-white p-1 rounded-sm text-sm" key={index}>{teeth}</span>
                  ))}
                </div>
              )}

              {casePrescriptionData.extraction.primaryDefinationTeeth && casePrescriptionData.extraction.primaryDefinationTeeth.length > 0 && (
                <div>
                  <div className="mb-1 font-semibold">Primary Defination:</div>
                  <div className="flex items-center gap-2">
                    {casePrescriptionData.extraction.primaryDefinationTeeth.map((teeth: string, index: number) => (
                      <span className="bg-orange text-white p-1 rounded-sm text-sm" key={index}>{teeth}</span>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      )
    },
    {
      title: "Bite Ramps",
      href: "/case-prescription",
      hasData: hasData(casePrescriptionData?.biteRamps?.option),
      content: (
        <div>
          {casePrescriptionData?.biteRamps?.option !== "place" && (
            <div className="font-medium">
              {casePrescriptionData?.biteRamps?.option === "auto" && (
                <>Automatically place precision bite ramps whenever lower incisor intrusion is more than 1.5 mm</>
              )}
              {casePrescriptionData?.biteRamps?.option === "none" && (
                <>{capitalizeFirstWord(casePrescriptionData?.biteRamps.option)}</>
              )}
            </div>
          )}

          {casePrescriptionData?.biteRamps?.option === "place" && (
            <div>
              <div className="mb-1 font-medium">
                <strong>Place Bite Ramps on lingual of these upper teeth</strong>
              </div>
              <div>
                {casePrescriptionData?.biteRamps?.selectedTooth && casePrescriptionData.biteRamps.selectedTooth.length > 0 ? (
                  casePrescriptionData?.biteRamps.selectedTooth.map((data, index) => (
                    <div key={index}><strong>{data}</strong></div>
                  ))
                ) : (
                  <div>None selected</div>
                )}
              </div>
            </div>
          )}
        </div>
      )
    },
    {
      title: "Special Note",
      href: "/case-prescription",
      hasData: hasData(casePrescriptionData?.specialNote?.note),
      content: (
        <div>
          {casePrescriptionData?.specialNote?.note || "None"}
        </div>
      ),
      border: false
    }
  ];

  // Filter out sections that don't have data
  const visibleSections = sections.filter(section => section.hasData);

  return (
    <div className="bg-[#F5F5F5] flex-grow flex flex-col">
      <div className="flex 2xl:flex-row flex-col justify-between 2xl:items-center 2xl:mb-6 mb-4">
        <h3 className="font-bold text-xl text-gray-800 2xl:mb-0 mb-2">Ali, Momen</h3>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-md flex flex-col flex-grow">
        <div className='grid !grid-cols-12 !gap-6 flex-grow'>
          <div className="relative xl:col-span-9 col-span-12 gap-4 rounded-[10px] text-dark">
            <div className='flex mb-5'>
              <h3 className='text-orange font-semibold text-base p-2 bg-[#FFF6F0] rounded-md'>Please, review the billing and shipping address of the order to ensure they are correct.</h3>
            </div>

            <div className='grid xl:grid-cols-3 grid-cols-2 gap-6 border-b-2 border-b-gray/50 pb-6 mb-5'>
              <SummaryCard heading='Shipping address' href='/patient-data' value={patientData.shipAddress} />
              <SummaryCard heading='Billing address' href='/patient-data' value={patientData.billAddress} borderNone={true} />
            </div>

            <div>
              <div className='flex flex-col gap-2'>
                {/* Render visible sections with dynamic numbering */}
                {visibleSections.map((section, index) => (
                  <SummaryCardNumbered 
                    key={index}
                    heading={`${index + 1}. ${section.title}`} 
                    sub={section.sub}
                    href={section.href}
                    border={section.border !== false}
                    content={section.content}
                  />
                ))}
              </div>
            </div>
          </div>
          <div className="xl:col-span-3 col-span-12 flex flex-col gap-4">
            <UploadedRecords withScan={false} disabled={false} none={true} />
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center mt-6">
        <button
          onClick={() => {}}
          className="px-6 py-2 border border-[#EB6309] text-[#EB6309] rounded-full font-medium hover:bg-[#FFF1E8] cursor-pointer"
        >
          Back
        </button>
        <div className="flex gap-4">
          <button className="px-6 py-2 border border-red-500 text-red-500 rounded-full font-medium hover:bg-red-100 cursor-pointer">
            Cancel
          </button>
          <button
            onClick={() => { router.push(`/patient-file?id=${patientId}`) }}
            className="px-6 py-2 bg-[#EB6309] text-white rounded-full font-medium hover:bg-[#d65a07] cursor-pointer"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
};

export default MainSummary;