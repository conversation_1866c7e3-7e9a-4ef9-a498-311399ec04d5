'use client'
import React, { useEffect, useState } from 'react'
import { z } from "zod";
import { FieldErrors, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
    attachmentRestrictionSchema,
    ExtractionSchema,
    movementRestrictionSchema,
    specialNoteSchema,
    stepOneSchema,
    TeethInformationSchema
} from "../ZodSchemas";
import ArchToTreat_1 from "../ArchToTreat_1";
import { toast } from "react-toastify";
import MovementResctiction_2 from "../MovementResctiction_2";
import AttachmentResctriction_3 from "../AttachmentRestriction_3";
import SpecialNote_10 from "../SpecialNote_10";
import TeethInformation from "../TeethInformation";
import Extraction from "../Extraction";
import { useRouter } from "next/navigation";
import FormWrapper from '@/components/reuseable/FormWrapper';
import getAndDecryptCookie, { storeToken } from '@/app/lib/auth';
import { submitCasePrescription } from '@/utils/ApisHelperFunction';

const ShortPrescriptionSchema = z.object({
    archToTreat: stepOneSchema,
    movementResctriction: movementRestrictionSchema,
    attachmentRestrictionSchema: attachmentRestrictionSchema,
    teethInformation: TeethInformationSchema,
    extraction: ExtractionSchema,
    specialNote: specialNoteSchema,
});

export type ShortPrescriptionSchemaType = z.infer<typeof ShortPrescriptionSchema>;

const defaultShortCasePrescriptionData: ShortPrescriptionSchemaType = {
    archToTreat: { option: "", value: "" },
    movementResctriction: { option: "none", restrictedTeeth: [], primaryDefinationTeeth: [] },
    attachmentRestrictionSchema: { option: "none", restrictedTeeth: [], primaryDefinationTeeth: [] },
    teethInformation: {
        missingTeethOption: "none",
        missingTeeth: [],
        primaryDefinationOption: "none",
        primaryDefinationTeeth: []
    },
    extraction: { option: "none", extractionTeeth: [], primaryDefinationTeeth: [] },
    specialNote: { note: '' },
};

const ShortVersion = () => {
    const [defaultValues, setDefaultValues] = useState<ShortPrescriptionSchemaType>(defaultShortCasePrescriptionData);
    const [isReady, setIsReady] = useState(false);

    const {
        register,
        watch,
        handleSubmit,
        setValue,
        reset,
        formState: { errors },
    } = useForm<ShortPrescriptionSchemaType>({
        resolver: zodResolver(ShortPrescriptionSchema),
        defaultValues: defaultValues,
        mode: "onChange"
    });

    useEffect(() => {
        const localData = localStorage.getItem("shortCasePrescriptionData");
        let parsed = defaultShortCasePrescriptionData;

        if (localData) {
            try {
                parsed = JSON.parse(localData);
                setDefaultValues(parsed);
            } catch (e) {
                console.error("Failed to parse localStorage data:", e);
            }
        }

        reset(parsed);
        setIsReady(true);

    }, [reset]);
    const router = useRouter();

const onSubmit = async (data: ShortPrescriptionSchemaType) => {
        console.log("Valid form data:", data);
        
        try {
            const token = getAndDecryptCookie("AccessToken");
            const patientId = getAndDecryptCookie("patientId");
            const version = "long";

            if (!token) {
                toast.error("Login is required.");
                return;
            }

            if (!patientId) {
                toast.error("Login is required.");
                return;
            }
            storeToken('LongcasePrescriptionData', JSON.stringify(data));

            const result = await submitCasePrescription(token, patientId, data, version);
            
            if (result) {
                // Success
                localStorage.removeItem("shortCasePrescriptionData");
                localStorage.setItem("casePrescriptionData", JSON.stringify(data));
                toast.success("Case prescription submitted successfully!");
                router.push("/summary");
            } else {
                // Error
                toast.error("Failed to submit case prescription. Please try again.");
            }
        } catch (error) {
            console.error("Error submitting case prescription:", error);
            toast.error("An error occurred while submitting case prescription.");
        }
    };

    const onError = (errors: FieldErrors<ShortPrescriptionSchemaType>) => {
        toast.error("Please Fill all the required fields")
        console.log("❌ Form validation errors:", errors);
    };

    if (!isReady) return <div>Loading...</div>;
    return (
        <FormWrapper classNames="!grid-cols-1" onSubmit={handleSubmit(onSubmit, onError)} onBack={() => { }}>
            <div className="">
                <div className="flex flex-col gap-8">
                    <ArchToTreat_1<ShortPrescriptionSchemaType> number='1' register={register} watch={watch} errors={errors} setValue={setValue} />
                    <TeethInformation<ShortPrescriptionSchemaType> number='2:' register={register} watch={watch} errors={errors} setValue={setValue} />
                    <MovementResctiction_2<ShortPrescriptionSchemaType> number='3:' register={register} watch={watch} errors={errors} setValue={setValue} />
                    <AttachmentResctriction_3<ShortPrescriptionSchemaType> number='4:' register={register} watch={watch} errors={errors} setValue={setValue} />
                    <Extraction<ShortPrescriptionSchemaType> number='5:' register={register} watch={watch} errors={errors} setValue={setValue} />
                    <SpecialNote_10<ShortPrescriptionSchemaType> number='6:' register={register} watch={watch} errors={errors} setValue={setValue} />
                </div>
            </div>
        </FormWrapper>
    )
}

export default ShortVersion
