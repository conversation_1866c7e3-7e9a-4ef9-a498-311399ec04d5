'use client'
import { StaticImport } from 'next/dist/shared/lib/get-img-props';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react'
import { z } from 'zod';
import FormWrapper from '../reuseable/FormWrapper';
import ScanSection from '../patient-records/ScanOptions';
import PhotosSection from '../patient-records/PhotosSection';
import { FieldErrors, useForm } from 'react-hook-form';
import { toast } from 'react-toastify';
import { zodResolver } from '@hookform/resolvers/zod';

export interface DentalPhoto {
    name: string;
    file: File | null;
}
export interface Placeholder {
    name: string;
    placeholder: StaticImport;
}
const fileSchema = z.instanceof(File).nullable();

const photoSchema = z.object({
    name: z.string(),
    file: fileSchema,
});

// Zod Schema
const baseSchema = {
    scan: z
        .object({
            stlFile1: fileSchema,
            stlFile2: fileSchema,
            cbctFile: fileSchema,
        })
        .superRefine((val, ctx) => {
            if (!val.stlFile1 || !val.stlFile2) {
                ctx.addIssue({
                    path: ["stlFile1"],
                    code: z.ZodIssueCode.custom,
                    message: "Both STL scans are required",
                });
            }
        }),
    photos: z.array(photoSchema).superRefine((val, ctx) => {
        const requiredPhotos = val.filter((photo) => photo.name !== "socialSmile");
        if (requiredPhotos.some((photo) => photo.file == null)) {
            ctx.addIssue({
                path: [],
                code: z.ZodIssueCode.custom,
                message: "All photos are required",
            });
        }
    }),
    // Add these fields to match the expected type in PhotosSection
    generalRecords: z.object({
        files: z.array(z.instanceof(File)).optional()
    }),
    // Add optional radiographs field
    radiographs: z.object({
        radioGraph1: fileSchema,
        radioGraph2: fileSchema,
    }).optional(),
};

// Final schema with optional radiographs
const PatientRecordsSchema = z.object({
    ...baseSchema,
});

export type ScanFormData = z.infer<typeof PatientRecordsSchema>;


const PatientRetainerRecord = () => {
    const router = useRouter();
    const [treatmentOption, setTreatmentOption] = useState<string | null>(null);

    useEffect(() => {
        const option = localStorage.getItem("treatmentOption");
        setTreatmentOption(option);
    }, []);
    console.log(treatmentOption)
    const {
        register,
        watch,
        setValue,
        handleSubmit,
        formState: { errors },
    } = useForm<ScanFormData>({
        resolver: zodResolver(PatientRecordsSchema),
        defaultValues: {
            scan: {
                stlFile1: null,
                stlFile2: null,
                cbctFile: null,
            },
            photos: [],
            // Add default values for the new fields
            generalRecords: {
                files: []
            }
            // radiographs is optional, so no need for a default
        },
        mode: "onChange",
    });

    const data = watch();

    useEffect(() => {
        console.log("DATA====>", data);
    }, [data]);

    const onSubmit = (data: ScanFormData) => {
        console.log("✅ Valid form data:", data);
        const retainerFormDataValueLocal = localStorage.getItem("retainerFormData")
        const treatmentOptionValueLocal = localStorage.getItem("treatmentOption");
        const patientDataValueLocal = localStorage.getItem("patientData");
        const valuesInLocalStorage = {
            patientData: patientDataValueLocal || {},
            retainerFormData: retainerFormDataValueLocal || {},
            treatmentOption: treatmentOptionValueLocal || null,
        };
        console.log("🚀 ~ onSubmit ~ valuesInLocalStorage:", valuesInLocalStorage)
        localStorage.removeItem("retainerFormData");
        localStorage.removeItem("treatmentOption");
        localStorage.removeItem("patientData");
        router.push("/patient-file");
    };

    const onError = (errors: FieldErrors<ScanFormData>) => {
        toast.error("Please fill all the required fields.");
        console.log("❌ Form validation errors:", errors);
    };

    return (
        <FormWrapper
            classNames="!grid-cols-1"
            onSubmit={handleSubmit(onSubmit, onError)}
            onBack={() => { }}
        >
            <div className="flex flex-col gap-6">
                <PhotosSection
                    register={register}
                    watch={watch}
                    setValue={setValue}
                    errors={errors}
                />
                <ScanSection
                    register={register}
                    watch={watch}
                    setValue={setValue}
                    errors={errors}
                    Enablecbct={false}
                />
            </div>
        </FormWrapper>
    )
}

export default PatientRetainerRecord
