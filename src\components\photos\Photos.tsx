"use client";
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import FormWrapper from '../reuseable/FormWrapper';
import RoundRadioButton from '../reuseable/RoundRadioButton';
import Image from 'next/image';
import closeIcon from "../../../public/svgs/icons8_close 1.svg"
import addIcon from "../../../public/svgs/icons8_add 1.svg"
import buccalLeft from "../../../public/svgs/buccal-left.svg";
import buccalRight from "../../../public/svgs/buccal-right.8f4707a1..svg";
import frontalRepose from "../../../public/svgs/frontal-repose.5f430b49..svg";
import frontalSmiling from "../../../public/svgs/frontal-smiling.6c08f65f..svg";
import labialAnterior from "../../../public/svgs/labial-anterior.9cf4e2c6..svg";
import occlussalLower from "../../../public/svgs/occlussal-lower.3be1bcdf..svg";
import occlussalUpper from "../../../public/svgs/occlussal-upper.cd664940..svg";
import profileRepose from "../../../public/svgs/profile-repose.cf7b4b65..svg";
import socialSmile from "../../../public/svgs/social-smile.ab9bc0e3..svg";
import UploadedRecords from '../reuseable/UploadedRecords';
import DisableOverlay from '../reuseable/DisableOverlay';
import { z } from 'zod';
import { StaticImport } from 'next/dist/shared/lib/get-img-props';
import { zodResolver } from '@hookform/resolvers/zod';


const photoRequirements: string[] = [
  "Please upload high resolution images (10 megapixel or higher) in JPG or PNG format.",
  "Please upload original photos, without any modifications.",
  "For any editing needed, please use the tools available as part of this photo uploader.",
  "Scan images cannot be used for Case Assessment.",
  "Images from iTero Element 5D systems and above can be used in lieu of intraoral photos.",
  "Photos can be added as desired."
];

type placeholder = {
name: string,
placeholder: StaticImport
}

const patientPhotos: placeholder[] = [
  { name: "profileRepose", placeholder: profileRepose },
  { name: "frontalRepose", placeholder: frontalRepose },
  { name: "frontalSmiling", placeholder: frontalSmiling },
  { name: "occlussalUpper", placeholder: occlussalUpper },
  { name: "socialSmile", placeholder: socialSmile },
  { name: "occlussalLower", placeholder: occlussalLower },
  { name: "buccalRight", placeholder: buccalRight },
  { name: "labialAnterior", placeholder: labialAnterior },
  { name: "buccalLeft", placeholder: buccalLeft },
];

type dentalPhoto = {
  name: string,
  file: File | null
}

interface OnSubmitData {
 uploadOption?: string
}

const patientDataSchema = z.object({
  uploadOption: z.string().min(1, "First name is required").optional()})

type UserFormData = z.infer<typeof patientDataSchema>;

const Photos = () => {
  const router = useRouter();
  const { register, handleSubmit } = useForm<UserFormData>({ resolver: zodResolver(patientDataSchema), });
  const [uploadOption, setUploadOption] = useState<string | undefined>('now');
  const [photos, setPhotos] = useState<dentalPhoto[]>([
    { name: "profileRepose", file: null },
    { name: "buccalRight", file: null },
    { name: "buccalLeft", file: null },
    { name: "frontalRepose", file: null },
    { name: "frontalSmiling", file: null },
    { name: "labialAnterior", file: null },
    { name: "occlussalLower", file: null },
    { name: "occlussalUpper", file: null },
    { name: "socialSmile", file: null }
  ])

  const onSubmit = (data: OnSubmitData) => {
    console.log("Valid form data:", { uploadOption: data.uploadOption, photos: photos });
    setUploadOption(data.uploadOption)
    localStorage.setItem("photos", JSON.stringify({ uploadOption: data.uploadOption, photos: photos }))
    router.push("/radiographs");
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, name: string) => {
    const files = Array.from(e.target.files || []);
    setPhotos((prev) => {
      const newArray = prev.map((photo: dentalPhoto) => {
        if (photo.name == name) {
          photo.file = files[0]
          return photo
        }
        return photo
      })
      return newArray
    })
  };


  const getImage = (photo: placeholder) => {
    const dentalPhoto = photos.find((p) => p.name === photo.name)
    if (dentalPhoto?.file) {
      const url = URL.createObjectURL(dentalPhoto.file)
      return url
    }
    return photo.placeholder
  }

  const getClasses = (photo: string | StaticImport) => {
    if (typeof photo === "string") {
      return "w-full aspect-[1/1] object-cover rounded-[inherit]"
    }
    return "w-[150px] h-[150px]"
  }
  const getClassesCard = (photo: string | StaticImport) => {
    console.log(photo)
    if (typeof photo === "string") {
      return "p-0 flex-row flex-grow items-center justify-center"
    }
    return "p-8 flex-col items-center justify-center"
  }
  const getWidth = (photo: string | StaticImport) => {
    if (typeof photo === "string") {
      return 250
    }
    return 150
  }
  const getHeight = (photo: string | StaticImport) => {
    if (typeof photo === "string") {
      return 250
    }
    return 150
  }


  const handleRemoveFile = (fileName: string) => {
    setPhotos((prev: dentalPhoto[]) => {
      const newArray = prev.map((photo: dentalPhoto) => {
        if (photo.name == fileName) {
          photo.file = null;
          return photo
        }
        return photo
      })
      return newArray
    })
  };

  return (
    <>
      <FormWrapper classNames='!grid-cols-1' onSubmit={handleSubmit(onSubmit)} onBack={() => router.back()}>
        <div className='col-span-1 flex flex-col flex-grow'>
          <div className='flex xl:gap-10 gap-4 flex-wrap mb-4'>
            <RoundRadioButton
              onClick={(e: React.MouseEvent<HTMLInputElement>) => setUploadOption((e.target as HTMLInputElement).value)}
              id="image-now"
              label="Upload photos now"
              value="now"
              name="uploadOption"
              register={register}
              defaultChecked={uploadOption === 'now'}
              labelClass='!text-dark font-medium'
            />
            <RoundRadioButton
              onClick={(e: React.MouseEvent<HTMLInputElement>) => setUploadOption((e.target as HTMLInputElement).value)}
              id="image-indivisual"
              label="Individual images"
              value="individual"
              name="uploadOption"
              register={register}
              defaultChecked={uploadOption === 'individual'}
              labelClass='!text-dark font-medium'
            />

            <RoundRadioButton
              onClick={(e: React.MouseEvent<HTMLInputElement>) => setUploadOption((e.target as HTMLInputElement).value)}
              id="image-composite"
              label="Composite"
              value="composite"
              name="uploadOption"
              register={register}
              defaultChecked={uploadOption === 'composite'}
              labelClass='!text-dark font-medium'
            />
            <RoundRadioButton
              onClick={(e: React.MouseEvent<HTMLInputElement>) => setUploadOption((e.target as HTMLInputElement).value)}
              id="image-none"
              label="Upload photos later"
              value="later"
              name="uploadOption"
              register={register}
              defaultChecked={uploadOption === 'later'}
              labelClass='!text-dark font-medium'
            />
          </div>

          <div className='grid !grid-cols-12 !gap-6 flex-grow'>
            <div className="relative xl:col-span-8 col-span-12 grid 2xl:grid-cols-3 xl:grid-cols-2 grid-cols-2 gap-4 p-8 !bg-primaryLight rounded-[10px]">
              <DisableOverlay active={uploadOption == "later"} color={"bg-black/40"} />
              {patientPhotos.map((photo, index) => {
                return (
                  <div key={index} className={`bg-white relative rounded-[10px] ${getClassesCard(getImage(photo))} col-span-1 w-full aspect-[1/1] flex `}>
                    <Image width={getWidth(getImage(photo))} height={getHeight(getImage(photo))} src={photos && getImage(photo)} alt={photo.name} className={getClasses(getImage(photo))} />
                    <div>
                      {uploadOption === "now" && (
                        <>
                          <div className='flex justify-end w-full absolute bottom-[3%] right-[3%]'>
                            {typeof getImage(photo) === "string" ? (
                              <button
                                type="button"
                                className=" bg-danger/50 text-white rounded-full p-1 w-5 h-5 flex items-center justify-center"
                                onClick={() => handleRemoveFile(photo.name)}
                              >
                                <Image src={closeIcon} alt='Cross icon' className='w-5 h-5 cursor-pointer' />

                              </button>
                            ) : (
                              <>
                                <label htmlFor={`upload-${photo.name}`} className='cursor-pointer'>
                                  <Image src={addIcon} alt='Upload icon' className='w-6 h-6' />
                                </label>
                                <input
                                  id={`upload-${photo.name}`}
                                  type="file"
                                  accept="image/*"
                                  onChange={(e) => handleFileChange(e, photo.name)}
                                  className="hidden"
                                />
                              </>
                            )}


                          </div>

                        </>

                      )}
                    </div>

                  </div>
                )
              })}

            </div>


            <div className="xl:col-span-4 col-span-12 flex flex-col gap-4">
              <UploadedRecords withScan={false} disabled={uploadOption == "later"} />
              <div className='!bg-primaryLight p-4 rounded-[10px] flex flex-col gap-2 relative'>
                <DisableOverlay active={uploadOption == "later"} color={"bg-black/40"} />
                <div className='flex flex-col'>
                  <p className='font-semibold text-lg text-dark'>Photo Requirements</p>
                  <ul className='text-dark font-medium text-sm list-disc ps-6'>
                    {photoRequirements.map((requirement: string, index: number) => {
                      return <li key={index} className=''>{requirement}</li>
                    })}
                  </ul>
                </div>
              </div>

            </div>



          </div>
        </div>

      </FormWrapper>
    </>
  );
};

export default Photos;
