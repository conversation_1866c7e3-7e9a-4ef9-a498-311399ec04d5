// import Note from "../reuseable/Note";
import { Path } from "react-hook-form";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import StepsCheckBoxRegister from "../reuseable/SquareCheckBoxRegister";
import { SpecialProps } from "./MovementResctiction_2";

const BiteRamps = <T extends { biteRamps?: { option?: string, selectedTooth?: string[] } },>({ register, errors, watch, number }: SpecialProps<T>) => {
    const biteRamp = watch("biteRamps.option" as Path<T>)
    // const tooth = watch("biteRamps.selectedTooth" as Path<T>)
    return (
        <div>
            <div className='flex items-center justify-between mb-4'>
                <h3 className="font-bold text-lg text-dark">
                    {`${number}`}{" "}Bite Ramps
                </h3>
            </div>

            <div className="flex flex-col gap-3">
                <div className="flex gap-3">
                    <RoundRadioButton
                        id="biteRamp-auto"
                        label="Automatically place precision bite ramps whenever lower incisor intrusion is more than 1.5 mm"
                        value="auto"
                        register={register}
                        name="biteRamps.option"
                        labelClass='!text-dark text-base'
                    />
                    <RoundRadioButton
                        id="biteRamp-place"
                        label="Place Bite Ramps on lingual of these upper teeth"
                        value="place"
                        register={register}
                        name="biteRamps.option"
                        labelClass='!text-dark text-base'
                    />


                    <RoundRadioButton
                        id="biteRamp-none"
                        label="None"
                        value="none"
                        register={register}
                        name="biteRamps.option"
                        labelClass='!text-dark text-base'
                    />
                </div>

                {biteRamp === 'place' && (
                    <div className="ml-6 ">
                        <div className="ml-6 flex gap-6">
                            <StepsCheckBoxRegister
                                label="Incisors"
                                id="incisors-checkbox"
                                value="incisors"
                                register={register("biteRamps.selectedTooth" as Path<T>)}
                                labelClass='!text-dark text-base'
                                rootLableClassName='!flex-row'
                                className='!w-5 !h-5'
                            />
                            <StepsCheckBoxRegister
                                label="Canines"
                                id="canines-checkbox"
                                value="canines"
                                register={register("biteRamps.selectedTooth" as Path<T>)}
                                labelClass='!text-dark text-base'
                                rootLableClassName='!flex-row'
                                className='!w-5 !h-5'
                            />
                        </div>


                        {errors.biteRamps && 'selectedTooth' in errors.biteRamps && errors.biteRamps.selectedTooth?.message && (
                            <p className="text-red-500 text-sm">{errors.biteRamps.selectedTooth.message as string}</p>
                        )}
                    </div>
                )}
                {errors.biteRamps && 'option' in errors.biteRamps && errors.biteRamps.option?.message && (
                    <p className="text-red-500 text-sm">{errors.biteRamps.option.message as string}</p>
                )}

                {/* <Note text={"In some cases, bite ramps placement may not be possible due to excessive overjet"} /> */}
            </div>

        </div>
    )
}

export default BiteRamps