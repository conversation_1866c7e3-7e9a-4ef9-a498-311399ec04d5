import { capitalizeFirstWord } from "@/utils/helperFunctions";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import { SpecialProps } from "./MovementResctiction_2";
import { ResolveField } from "./ZodSchemas";
import { FieldErrors, FieldValues, Path } from "react-hook-form";

import StepsCheckBoxRegister from "../reuseable/SquareCheckBoxRegister";
type ResolveUpperType = {
    expand?: string;
    procline?: string;
    iprAnterior?: string;
    molarDistalization?: string;
};
type ResolveLowerType = {
    expand?: string;
    procline?: string;
    iprAnterior?: string;
    molarDistalization?: string;
};
const Spacing_9 = <T extends FieldValues>({ register, errors, watch, number }: SpecialProps<T>) => {

    const watchedUpper = (watch("spacing.resolveUpper" as Path<T>) as ResolveUpperType) || {};
    const errorUpper: FieldErrors<ResolveUpperType> =
        (errors.spacing && typeof errors.spacing === "object" && "resolveUpper" in errors.spacing)
            ? (errors.spacing.resolveUpper as FieldErrors<ResolveUpperType>)
            : {};


    const resolveUpperFields: ResolveField[] = [
        {
            label: "Expand",
            name: "expand",
            value: watchedUpper.expand ?? "none",
            error: errorUpper.expand,
        },
        {
            label: "Procline",
            name: "procline",
            value: watchedUpper.procline ?? "none",
            error: errorUpper.procline,
        },
        {
            label: "IPR",
            name: "iprAnterior",
            value: watchedUpper.iprAnterior ?? "none",
            error: errorUpper.iprAnterior,
        },
        {
            label: "Molar Distalization",
            name: "molarDistalization",
            value: watchedUpper.molarDistalization ?? "",
            error: errorUpper.molarDistalization,
        },
    ];

    const watchedLower = (watch("spacing.resolveLower" as Path<T>) as ResolveLowerType) || {};
    const errorLower: FieldErrors<ResolveLowerType> =
        (errors.spacing && typeof errors.spacing === "object" && "resolveLower" in errors.spacing)
            ? (errors.spacing.resolveLower as FieldErrors<ResolveLowerType>)
            : {};
    const resolveLowerFields: ResolveField[] = [
        {
            label: "Expand",
            name: "expand",
            value: watchedLower.expand ?? "none",
            error: errorLower.expand,
        },
        {
            label: "Procline",
            name: "procline",
            value: watchedLower.procline ?? "none",
            error: errorLower.procline,
        },
        {
            label: "IPR",
            name: "iprAnterior",
            value: watchedLower.iprAnterior ?? "none",
            error: errorLower.iprAnterior,
        },
        {
            label: "Molar Distalization",
            name: "molarDistalization",
            value: watchedLower.molarDistalization ?? "",
            error: errorLower.molarDistalization,
        },
    ];

    const spacingOption = watch("spacing.option" as Path<T>)
    return (
        <div>
            <div>
                <div className='flex items-center justify-between mb-4'>
                    <h3 className="font-bold text-lg  text-dark">{`${number}`}{" "}Spacing & Crowding (Arch Length Discrepancy)</h3>
                </div>
            </div>

            <div className="mb-4 space-y-3">
                <p className="text-dark font-bold text-lg">Spacing</p>
                <div className="flex gap-6">
                    <RoundRadioButton
                        id="Decide after 3D Simulation"
                        label="Decide after 3D Simulation"
                        value="Decide after 3D Simulation"
                        register={register}
                        name="spacing.option"
                        labelClass='!text-dark text-base'
                    />
                    <RoundRadioButton
                        id="closeAllSpaces"
                        label="Close all spaces"
                        value="Close all spaces"
                        register={register}
                        name="spacing.option"
                        labelClass='!text-dark text-base'
                    />
                    <RoundRadioButton
                        id="LeaveSpaces"
                        label="Leave Spaces"
                        value="Leave spaces"
                        register={register}
                        name="spacing.option"
                        labelClass='!text-dark text-base'
                    />
                </div>
                {spacingOption == "Leave spaces" && (
                    <div>
                        <textarea
                            placeholder="Note"
                            {...register("spacing.note" as Path<T>)}
                            className="border p-2 w-full rounded-xl mt-2"
                        />
                    </div>
                )}
                {spacingOption === "Close all spaces" && (
                    <div className="flex flex-col gap-2 ps-6">
                        {["upper", "lower"].map((side: string, index: number) => (
                            <div key={index}>
                                <div className="flex  gap-5">
                                    <span>{capitalizeFirstWord(side)}</span>
                                    <div className="flex gap-3 ps-5">
                                        <RoundRadioButton
                                            id={`Retract Anterior-${side}-${index}`}
                                            label="Retract Anterior"
                                            value="Retract Anterior"
                                            register={register}
                                            name={`spacing.${side}`}
                                            labelClass='!text-dark text-base'
                                        />
                                        <RoundRadioButton
                                            id={`Retract Anterior and Mesialize Posterior-${side}-${index}`}
                                            label="Retract Anterior and Mesialize Posterior"
                                            value="Retract Anterior and Mesialize Posterior"
                                            register={register}
                                            name={`spacing.${side}` as Path<T>}
                                            labelClass='!text-[#434343] text-base'
                                        />
                                        <RoundRadioButton
                                            id={`Mesialize Posterior-${side}-${index}`}
                                            label="Mesialize Posterior"
                                            value="Mesialize Posterior"
                                            register={register}
                                            name={`spacing.${side}` as Path<T>}
                                            labelClass='!text-[#434343] text-base'
                                        />
                                    </div>
                                </div>
                                {watch(`overbite.${side}.others` as Path<T>) && (
                                    <div>
                                        <textarea
                                            placeholder="Note"
                                            {...register(`overbite${side}.otherNote` as Path<T>)}
                                            className="border p-2 w-full rounded-xl mt-2"
                                        />
                                    </div>
                                )}
                            </div>
                        ))}
                    </div>
                )}
                {typeof errors.spacing === "object" &&
                    errors.spacing !== null &&
                    "option" in errors.spacing &&
                    errors.spacing.option &&
                    typeof errors.spacing.option === "object" &&
                    errors.spacing.option !== null &&
                    "message" in errors.spacing.option &&
                    errors.spacing.option.message && (
                        <p className="text-red-500 text-sm mt-1">
                            {String(errors.spacing.option.message)}
                        </p>
                    )}
            </div>

            <div>
                <p className="text-dark font-bold text-lg">Crowding</p>
                <div className="mt-3">
                    <p className="text-black font-bold text-xl mb-1">Resolve Upper</p>

                    <div className="mt-2 space-y-4">
                        {resolveUpperFields.map(({ label, name, error }) => (
                            <div key={name}>
                                <div className="flex items-center ">
                                    <span className="text-dark font-medium text-base w-[30%]">{label}</span>
                                    <div className="flex gap-6">
                                        {name === "molarDistalization" ? (
                                            <>
                                                <StepsCheckBoxRegister
                                                    id={`${name}-upper-right`}
                                                    label="Upper Right"
                                                    value="Upper Right"
                                                    register={register(`spacing.resolveUpper.${name}` as Path<T>)}
                                                    name={`spacing.resolveUpper.${name}`}
                                                    labelClass="!text-dark text-base"
                                                    rootLableClassName="!flex-row"
                                                />
                                                <StepsCheckBoxRegister
                                                    id={`${name}-upper-left`}
                                                    label="Upper Left"
                                                    value="Upper Left"
                                                    register={register(`spacing.resolveUpper.${name}` as Path<T>)}
                                                    name={`spacing.resolveUpper.${name}`}
                                                    labelClass="!text-dark text-base"
                                                    rootLableClassName="!flex-row"
                                                />
                                            </>
                                        ) : (
                                            ["primarily", "asNeeded", "none"].map((option) => (
                                                <RoundRadioButton
                                                    key={option}
                                                    id={`${name}${option}-upper`}
                                                    label={option === "asNeeded" ? "As needed" : option.charAt(0).toUpperCase() + option.slice(1)}
                                                    value={option}
                                                    register={register}
                                                    name={`spacing.resolveUpper.${name}`}
                                                    labelClass="!text-dark text-base"
                                                />
                                            ))
                                        )}
                                    </div>
                                </div>
                                {error && <p className="text-red-500 text-sm mt-1">{error.message}</p>}
                            </div>
                        ))}
                    </div>
                </div>

                <div className="mt-6">
                    <p className="text-black font-bold text-xl mb-1">Resolve Lower</p>
                    <div className="mt-2 space-y-4">
                        {resolveLowerFields.map(({ label, name,
                            // value,
                            error }) => (
                            <div key={name}>
                                <div className="flex items-center">
                                    <span className="text-dark font-medium text-base w-[30%]">{label}</span>
                                    <div className="flex gap-6">
                                        {name === "molarDistalization" ? (
                                            <>
                                                <StepsCheckBoxRegister
                                                    id={`${name}-Lower-right`}
                                                    label="Lower Right"
                                                    value="Lower Right"
                                                    register={register(`spacing.resolveLower.${name}` as Path<T>)}
                                                    name={`spacing.resolveLower.${name}`}
                                                    labelClass="!text-dark text-base"
                                                    rootLableClassName="!flex-row"
                                                />
                                                <StepsCheckBoxRegister
                                                    id={`${name}-Lower-left`}
                                                    label="Lower Left"
                                                    value="Lower Left"
                                                    register={register(`spacing.resolveLower.${name}` as Path<T>)}
                                                    name={`spacing.resolveLower.${name}`}
                                                    labelClass="!text-dark text-base"
                                                    rootLableClassName="!flex-row"
                                                />
                                            </>
                                        ) : (
                                            ["primarily", "asNeeded", "none"].map((option) => (
                                                <RoundRadioButton
                                                    key={option}
                                                    id={`${name}${option}-lower`}
                                                    label={option === "asNeeded" ? "As needed" : option.charAt(0).toUpperCase() + option.slice(1)}
                                                    value={option}
                                                    register={register}
                                                    name={`spacing.resolveLower.${name}`}
                                                    labelClass="!text-dark text-base"
                                                />
                                            ))
                                        )}
                                    </div>
                                </div>
                                {error && <p className="text-red-500 text-sm mt-1">{error.message}</p>}
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Spacing_9