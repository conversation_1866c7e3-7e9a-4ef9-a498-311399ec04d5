import { <PERSON><PERSON><PERSON><PERSON>, UseFormReg<PERSON>, UseFormSetValue, UseFormWatch } from "react-hook-form";
import { Dental<PERSON>hoto, Placeholder, ScanFormData } from "./PatientRecords";
import React, { useEffect, useState } from "react";
import { StaticImport } from "next/dist/shared/lib/get-img-props";
import buccalLeft from "../../../public/svgs/buccal-left.svg";
import buccalRight from "../../../public/svgs/buccal-right.8f4707a1..svg";
import frontalRepose from "../../../public/svgs/frontal-repose.5f430b49..svg";
import frontalSmiling from "../../../public/svgs/frontal-smiling.6c08f65f..svg";
import labialAnterior from "../../../public/svgs/labial-anterior.9cf4e2c6..svg";
import occlussalLower from "../../../public/svgs/occlussal-lower.3be1bcdf..svg";
import occlussalUpper from "../../../public/svgs/occlussal-upper.cd664940..svg";
import profileRepose from "../../../public/svgs/profile-repose.cf7b4b65..svg";
import Image from "next/image";
import closeIcon from "../../../public/svgs/icons8_close 1.svg";
import addIcon from "../../../public/svgs/icons8_add 1.svg";
import logo from "../../../public/images/logo.png";

interface PhotosSectionProps {
    register: UseFormRegister<ScanFormData>;
    watch: UseFormWatch<ScanFormData>;
    setValue: UseFormSetValue<ScanFormData>
    errors: FieldErrors<ScanFormData>
}
// Define patient photos outside the component to prevent re-creation
const patientPhotos: Placeholder[] = [
    { name: "profileRepose", placeholder: profileRepose },
    { name: "frontalRepose", placeholder: frontalRepose },
    { name: "frontalSmiling", placeholder: frontalSmiling },
    { name: "occlussalUpper", placeholder: occlussalUpper },
    { name: "socialSmile", placeholder: logo },
    { name: "occlussalLower", placeholder: occlussalLower },
    { name: "buccalRight", placeholder: buccalRight },
    { name: "labialAnterior", placeholder: labialAnterior },
    { name: "buccalLeft", placeholder: buccalLeft },
];

const PhotosSection: React.FC<PhotosSectionProps> = ({
    setValue,
    errors,
}) => {
    const [photos, setPhotos] = useState<DentalPhoto[]>([
        { name: "profileRepose", file: null },
        { name: "buccalRight", file: null },
        { name: "buccalLeft", file: null },
        { name: "frontalRepose", file: null },
        { name: "frontalSmiling", file: null },
        { name: "labialAnterior", file: null },
        { name: "occlussalLower", file: null },
        { name: "occlussalUpper", file: null },
        { name: "socialSmile", file: null },
    ]);

    const [photoURLs, setPhotoURLs] = useState<{ [key: string]: string }>({});

    // Cleanup blob URLs to prevent memory leaks
    useEffect(() => {
        return () => {
            Object.values(photoURLs).forEach((url) => URL.revokeObjectURL(url));
        };
    }, [photoURLs]);

    // Generate preview URLs when photos change
    useEffect(() => {
        const newURLs: { [key: string]: string } = {};

        photos.forEach((photo) => {
            if (photo.file) {
                if (photoURLs[photo.name]) {
                    URL.revokeObjectURL(photoURLs[photo.name]);
                }
                newURLs[photo.name] = URL.createObjectURL(photo.file);
            }
        });

        setPhotoURLs((prev) => ({
            ...prev,
            ...newURLs,
        }));

        console.log("🚀 ~ useEffect ~ newURLs:", newURLs)
        // Update the react-hook-form state
        setValue("photos", photos);
    }, [photos, setValue]);
        console.log("🚀 ~ useEffect ~ photos:", photos)

    const handlePhotoFileChange = (
        e: React.ChangeEvent<HTMLInputElement>,
        name: string
    ) => {
        const file = e.target.files?.[0];
        if (!file) return;

        setPhotos((prev) =>
            prev.map((photo) =>
                photo.name === name ? { ...photo, file } : photo
            )
        );
    };

    const handleDeletePhoto = (name: string) => {
        setPhotos((prev) => {
            const newPhotos = prev.map((photo) =>
                photo.name === name ? { ...photo, file: null } : photo
            );

            setPhotoURLs((prevURLs) => {
                const updated = { ...prevURLs };
                if (updated[name]) {
                    URL.revokeObjectURL(updated[name]);
                    delete updated[name];
                }
                return updated;
            });

            return newPhotos;
        });
    };

    const getImage = (photo: Placeholder): StaticImport | string => {
        const dentalPhoto = photos.find((p) => p.name === photo.name);
        return dentalPhoto?.file && photoURLs[photo.name]
            ? photoURLs[photo.name]
            : photo.placeholder;
    };

    return (
        <div className="mb-8">
            <h2 className="text-2xl font-bold text-dark mb-4">Photos</h2>
            <div className="relative grid 2xl:grid-cols-3 xl:grid-cols-2 grid-cols-2 gap-4 p-8 !bg-primaryLight rounded-[10px]">
                {patientPhotos.map((photo, index) => (
                    photo.name === "socialSmile" ? (
                        <div
                            key={index}
                            className="bg-white h-[200px] relative rounded-[10px] p-8 flex flex-col items-center justify-center"
                        >
                            <Image
                                width={1000}
                                height={1000}
                                src={photo.placeholder}
                                alt={photo.name}
                                className="w-full"
                            />
                        </div>
                    ) : (
                        <label
                            htmlFor={`upload-${photo.name}`}
                            key={index}
                            className={`bg-white h-[200px] relative cursor-pointer rounded-[10px] ${photos.find(p => p.name === photo.name)?.file ? "p-0" : "p-8"
                                } flex flex-col items-center justify-center`}
                        >
                            <Image
                                width={150}
                                height={150}
                                src={getImage(photo)}
                                alt={photo.name}
                                className={
                                    photos.find(p => p.name === photo.name)?.file
                                        ? "w-full h-full object-cover rounded-[inherit]"
                                        : "w-[150px] h-[150px]"
                                }
                            />

                            <div className="flex justify-end w-full absolute bottom-[3%] right-[3%]">
                                {photos.find(p => p.name === photo.name)?.file ? (
                                    <button
                                        onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            handleDeletePhoto(photo.name);
                                        }}
                                        type="button"
                                        className="bg-danger/50 text-white rounded-full p-1 w-5 h-5 flex items-center justify-center"
                                    >
                                        <Image src={closeIcon} alt="Cross icon" className="w-5 h-5 cursor-pointer" />
                                    </button>
                                ) : (
                                    <>
                                        <label htmlFor={`upload-${photo.name}`} className="cursor-pointer">
                                            <Image src={addIcon} alt="Upload icon" className="w-6 h-6" />
                                        </label>
                                        <input
                                            id={`upload-${photo.name}`}
                                            type="file"
                                            accept="image/*"
                                            onChange={(e) => handlePhotoFileChange(e, photo.name)}
                                            className="hidden"
                                        />
                                    </>
                                )}
                            </div>
                        </label>
                    )
                ))}
            </div>
            {errors.photos?.message && <p className="text-danger my-1">{errors.photos?.message}</p>}
        </div>
    );
};

export default PhotosSection;