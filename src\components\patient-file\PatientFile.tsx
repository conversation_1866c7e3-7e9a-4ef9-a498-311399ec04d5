'use client'
import Image from 'next/image';
import Header from '../reuseable/Header';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import ReplacementModel from './ReplacementModel';
import RefinementModel from './RefinementModel';
import RetainerRequestModal from './RetainerRequestModal';
import UploadCbctModal from './UploadCbctModal';

// Import photo placeholders
import buccalLeft from "../../../public/svgs/buccal-left.svg";
import buccalRight from "../../../public/svgs/buccal-right.8f4707a1..svg";
import frontalRepose from "../../../public/svgs/frontal-repose.5f430b49..svg";
import frontalSmiling from "../../../public/svgs/frontal-smiling.6c08f65f..svg";
import labialAnterior from "../../../public/svgs/labial-anterior.9cf4e2c6..svg";
import occlussalLower from "../../../public/svgs/occlussal-lower.3be1bcdf..svg";
import occlussalUpper from "../../../public/svgs/occlussal-upper.cd664940..svg";
import profileRepose from "../../../public/svgs/profile-repose.cf7b4b65..svg";
import socialSmile from "../../../public/svgs/social-smile.ab9bc0e3..svg";
import { PatientFileData } from '@/types/types';
// import fileIcon from "../../../public/svgs/file-icon.svg";
// import radiographIcon from "../../../public/svgs/xray-icon.svg";

type PatientFileButton = {
    text: string;
    action: () => void;
};

type TabType = 'summary' | 'records' | 'conversations';

export interface PatientFileProps {
    data: PatientFileData;
}

// Records Tab Component
const RecordsTab = () => {
    // Sample data - in a real app, this would come from an API or props
    const photos = [
        { name: "Profile Repose", img: profileRepose, date: "12/03/2023" },
        { name: "Frontal Repose", img: frontalRepose, date: "12/03/2023" },
        { name: "Frontal Smiling", img: frontalSmiling, date: "12/03/2023" },
        { name: "Occlussal Upper", img: occlussalUpper, date: "12/03/2023" },
        { name: "Social Smile", img: socialSmile, date: "12/03/2023" },
        { name: "Occlussal Lower", img: occlussalLower, date: "12/03/2023" },
        { name: "Buccal Right", img: buccalRight, date: "12/03/2023" },
        { name: "Labial Anterior", img: labialAnterior, date: "12/03/2023" },
        { name: "Buccal Left", img: buccalLeft, date: "12/03/2023" }
    ];

    const stlFiles = [
        { name: "Upper STL", date: "12/03/2023", size: "5.4 MB" },
        { name: "Lower STL", date: "12/03/2023", size: "4.8 MB" }
    ];

    const radiographs = [
        { name: "Radiograph 1", date: "12/03/2023", size: "8.2 MB" },
        { name: "Radiograph 2", date: "12/03/2023", size: "7.5 MB" }
    ];

    const [isAccordionOpen, setIsAccordionOpen] = useState(true);

    const toggleAccordion = () => {
        setIsAccordionOpen(!isAccordionOpen);
    };

    return (
        <div className="p-4 space-y-4">
            {/* Single Accordion for All Records */}
            <div className="border border-gray-200 rounded-xl overflow-hidden">
                <div
                    className={`flex justify-between items-center p-4 cursor-pointer ${isAccordionOpen ? 'bg-orange-50' : 'bg-white'}`}
                    onClick={toggleAccordion}
                >
                    <h3 className="font-semibold text-gray-800">Patient Records</h3>
                    <svg
                        className={`w-5 h-5 transition-transform ${isAccordionOpen ? 'transform rotate-180' : ''}`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>

                {isAccordionOpen && (
                    <div className="p-4 bg-white space-y-6">
                        {/* Photos Section */}
                        <div>
                            <h4 className="font-medium text-gray-700 mb-3 pb-2 border-b">Photos</h4>
                            <div className="grid grid-cols-3 gap-4">
                                {photos.map((photo, index) => (
                                    <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
                                        <div className="h-40 relative">
                                            <Image
                                                src={photo.img}
                                                alt={photo.name}
                                                fill
                                                className="object-fill"
                                            />
                                        </div>
                                        <div className="p-2">
                                            <h4 className="text-sm font-medium text-gray-800">{photo.name}</h4>
                                            <p className="text-xs text-gray-500">Added: {photo.date}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* STL Files Section */}
                        <div>
                            <h4 className="font-medium text-gray-700 mb-3 pb-2 border-b">STL Files</h4>
                            <div className="space-y-3">
                                {stlFiles.map((file, index) => (
                                    <div key={index} className="flex items-center p-3 border border-gray-200 rounded-lg">
                                        <div className="h-10 w-10 relative flex-shrink-0">
                                            {/* File Icon */}
                                            <div className="bg-orange-100 rounded-full w-10 h-10 flex items-center justify-center">
                                                <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div className="ml-3 flex-grow">
                                            <h4 className="text-sm font-medium text-gray-800">{file.name}</h4>
                                            <p className="text-xs text-gray-500">Added: {file.date} • {file.size}</p>
                                        </div>
                                        <button className="px-3 py-1 text-xs bg-primary text-white rounded-full">
                                            Download
                                        </button>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Radiographs Section */}
                        <div>
                            <h4 className="font-medium text-gray-700 mb-3 pb-2 border-b">Radiographs</h4>
                            <div className="space-y-3">
                                {radiographs.map((file, index) => (
                                    <div key={index} className="flex items-center p-3 border border-gray-200 rounded-lg">
                                        <div className="h-10 w-10 relative flex-shrink-0">
                                            {/* Radiograph Icon */}
                                            <div className="bg-orange-100 rounded-full w-10 h-10 flex items-center justify-center">
                                                <svg className="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                                </svg>
                                            </div>
                                        </div>
                                        <div className="ml-3 flex-grow">
                                            <h4 className="text-sm font-medium text-gray-800">{file.name}</h4>
                                            <p className="text-xs text-gray-500">Added: {file.date} • {file.size}</p>
                                        </div>
                                        <button className="px-3 py-1 text-xs bg-primary text-white rounded-full">
                                            View
                                        </button>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

// Conversations Tab Component
const ConversationsTab = () => {
    // Sample conversation data
    const [conversations, setConversations] = useState([
        {
            id: 1,
            sender: "Dr. Smith",
            date: "18 Jun 2023",
            time: "14:30",
            message: "Patient is progressing well with the current treatment plan. The upper aligners are tracking perfectly.",
            isDoctor: true
        },
        {
            id: 2,
            sender: "Lab Technician",
            date: "17 Jun 2023",
            time: "10:15",
            message: "New scans received. We've noted the request for additional refinement on the lower arch. Processing will begin today.",
            isDoctor: false
        },
        {
            id: 3,
            sender: "Dr. Smith",
            date: "15 Jun 2023",
            time: "09:45",
            message: "Please check the fit on the next set of aligners. Patient reported some discomfort with the current set.",
            isDoctor: true
        }
    ]);

    // State for the message input
    const [messageInput, setMessageInput] = useState("");

    // Function to add a new message
    const addMessage = (e: React.FormEvent) => {
        e.preventDefault();

        if (messageInput.trim() === "") return;

        // Get current date and time
        const now = new Date();
        const date = now.toLocaleDateString('en-GB', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        }).replace(/\//g, ' ');
        const time = now.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });

        // Create new message
        const newMessage = {
            id: conversations.length + 1,
            sender: "Dr. Smith", // Assuming the current user is the doctor
            date,
            time,
            message: messageInput,
            isDoctor: true
        };

        // Add new message to conversations
        setConversations([...conversations, newMessage]);

        // Clear input
        setMessageInput("");
    };

    return (
        <div className="p-4">
            <div className="mb-4 flex justify-between items-center">
                <h3 className="font-semibold text-gray-800">Recent Conversations</h3>
            </div>

            <div className="space-y-4 h-80 overflow-y-auto px-3">
                {conversations.map((convo) => (
                    <div
                        key={convo.id}
                        className={`p-4 rounded-lg ${convo.isDoctor ? 'bg-orange-50 ml-12' : 'bg-gray-50 mr-12'}`}
                    >
                        <div className="flex justify-between items-center mb-2">
                            <span className="font-medium text-sm">{convo.sender}</span>
                            <span className="text-xs text-gray-500">{convo.date} at {convo.time}</span>
                        </div>
                        <p className="text-sm text-gray-700">{convo.message}</p>
                    </div>
                ))}
            </div>

            {/* Message input form */}
            <form onSubmit={addMessage} className="mt-6 flex gap-2">
                <input
                    type="text"
                    placeholder="Type your message..."
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    className="flex-grow p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                />
                <button
                    type="submit"
                    className="p-3 bg-primary text-white rounded-lg"
                >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                    </svg>
                </button>
            </form>
        </div>
    );
};

const PatientFile: React.FC<PatientFileProps> = ({ data }) => {
    console.log("🚀 ~ data:", data)
    const [showReplacementModel, setShowReplaceMentModel] = useState(false);
    const [showRefineentModel, setShowRefinementModel] = useState(false);
    const [showRetainerModel, setShowRetainerModel] = useState(false);
    const [showCbctModal, setShowCbctModal] = useState(false);
    const [activeTab, setActiveTab] = useState<TabType>('summary');
    const router = useRouter();

    const handleClick = () => {
        router.push("/3d-preview");
    };

    const allButtons: PatientFileButton[] = [
        { text: 'Refinement Aligner', action: () => { setShowRefinementModel(true) } },
        { text: 'Aligner Replacement', action: () => { setShowReplaceMentModel(true) } },
        { text: '4D Graphy Retainers', action: () => { setShowRetainerModel(true) } },
        { text: 'Upload CBCT', action: () => { setShowCbctModal(true) } },
    ];

    return (
        <div className="p-4 min-h-screen">
            <div className="mb-3">
                <Header onSearchChange={() => { }} searchValue={""} />
            </div>

            {showReplacementModel && <ReplacementModel onClose={() => setShowReplaceMentModel(false)} />}
            {showRefineentModel && <RefinementModel onClose={() => setShowRefinementModel(false)} />}
            {showRetainerModel && <RetainerRequestModal onClose={() => setShowRetainerModel(false)} />}
            {showCbctModal && <UploadCbctModal onClose={() => setShowCbctModal(false)} />}

            <div className='flex justify-between items-center mb-4'>
                <div className='text-[28px] text-[#444443] font-semibold'>Patient File</div>
                <div className='flex gap-2'>
                    <button
                        type='button'
                        className={`flex items-center justify-center gap-2 !py-2 !px-8 !min-w-0 cursor-pointer rounded-full bg-primary hover:bg-[#D45A08] transition`}>
                        <span className="font-semibold text-lg text-white">Edit</span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M19.3287 9.26799L20.5487 8.048C21.8157 6.781 21.8157 4.719 20.5487 3.4515C19.9352 2.8385 19.1197 2.5015 18.2502 2.5015C17.3807 2.5015 16.5647 2.839 15.9517 3.452L14.7322 4.6715L19.3287 9.26799ZM13.6717 5.732L4.63723 14.7665C4.44473 14.959 4.29823 15.1965 4.21323 15.454L2.53823 20.5145C2.44873 20.7835 2.51923 21.08 2.71973 21.2805C2.86323 21.4235 3.05423 21.5 3.25023 21.5C3.32923 21.5 3.40873 21.4875 3.48623 21.462L8.54523 19.7865C8.80373 19.7015 9.04173 19.555 9.23423 19.362L18.2682 10.328L13.6717 5.732Z" fill="white" />
                        </svg>
                    </button>
                    <button
                        type='button'
                        className={`flex items-center justify-center gap-2 !py-2 !px-8 !min-w-0 cursor-pointer rounded-full bg-primary hover:bg-[#D45A08] transition`}>
                        <span className="font-semibold text-lg text-white">Archive</span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M6.25 2C5.0075 2 4 3.0075 4 4.25V19.75C4 20.9925 5.0075 22 6.25 22H17.75C18.9925 22 20 20.9925 20 19.75V4.25C20 3.0075 18.9925 2 17.75 2H14.5V2.75C14.5 3.1645 14.164 3.5 13.75 3.5H10.25C9.836 3.5 9.5 3.1645 9.5 2.75V2H6.25ZM10.25 4.5H13.75C14.164 4.5 14.5 4.8355 14.5 5.25C14.5 5.6645 14.164 6 13.75 6H10.25C9.836 6 9.5 5.6645 9.5 5.25C9.5 4.8355 9.836 4.5 10.25 4.5ZM10.25 7H13.75C14.164 7 14.5 7.3355 14.5 7.75C14.5 8.1645 14.164 8.5 13.75 8.5H10.25C9.836 8.5 9.5 8.1645 9.5 7.75C9.5 7.3355 9.836 7 10.25 7ZM10.25 9.5H13.75C14.164 9.5 14.5 9.8355 14.5 10.25C14.5 10.6645 14.164 11 13.75 11H10.25C9.836 11 9.5 10.6645 9.5 10.25C9.5 9.8355 9.836 9.5 10.25 9.5ZM10.7744 12H13.2256C13.3991 12 13.5594 12.0874 13.6504 12.2354C14.0299 12.8529 15 14.57 15 16C15 17.6545 13.6545 19 12 19C10.3455 19 9 17.6545 9 16C9 14.57 9.97011 12.8529 10.3496 12.2354C10.4406 12.0874 10.6009 12 10.7744 12ZM12 15C11.7348 15 11.4804 15.1054 11.2929 15.2929C11.1054 15.4804 11 15.7348 11 16C11 16.2652 11.1054 16.5196 11.2929 16.7071C11.4804 16.8946 11.7348 17 12 17C12.2652 17 12.5196 16.8946 12.7071 16.7071C12.8946 16.5196 13 16.2652 13 16C13 15.7348 12.8946 15.4804 12.7071 15.2929C12.5196 15.1054 12.2652 15 12 15Z" fill="white" />
                        </svg>
                    </button>
                </div>
            </div>

            {/* Tab Navigation */}
            <div className="mb-4 border-b border-gray-200">
                <nav className="flex space-x-6" aria-label="Tabs">
                    <button
                        onClick={() => setActiveTab('summary')}
                        className={`py-3 px-1 border-b-2 font-medium text-sm ${activeTab === 'summary'
                                ? 'border-primary text-primary'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                    >
                        Summary
                    </button>
                    <button
                        onClick={() => setActiveTab('records')}
                        className={`py-3 px-1 border-b-2 font-medium text-sm ${activeTab === 'records'
                                ? 'border-primary text-primary'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                    >
                        Records
                    </button>
                    <button
                        onClick={() => setActiveTab('conversations')}
                        className={`py-3 px-1 border-b-2 font-medium text-sm ${activeTab === 'conversations'
                                ? 'border-primary text-primary'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                            }`}
                    >
                        Conversations
                    </button>
                </nav>
            </div>

            <div className="bg-white rounded-[16px] p-3 grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Main Content Area - Changes based on active tab */}
                <div className="col-span-2 space-y-6">
                    {activeTab === 'summary' && (
                        <div className="p-6 rounded-xl">
                            {/* Patient Info */}
                            <div className="flex items-center gap-4">
                                <Image
                                    src={data.profileRepose || `https://ui-avatars.com/api/?name=${data.first_name}+${data.last_name}`}
                                    alt="profile"
                                    width={60}
                                    height={60}
                                    className="rounded-full"
                                />
                                <div>
                                    <p className="text-sm text-gray-800 font-semibold">Name: {data.first_name}, {data.last_name}</p>
                                    <p className="text-sm text-gray-500">Date of Birth: {new Date(data.dob).toLocaleDateString()}</p>
                                </div>
                                <div className="ml-auto text-sm text-gray-600 space-y-1">
                                    <p>Patient: #<strong>{data.uuid}</strong></p>
                                    <p>Ship to Office: <strong>{data.shipToOffice?.clinic_name + ", " + data.shipToOffice?.street_address + ", " + data.shipToOffice?.city + ", " + data.shipToOffice?.postal_code || "N/A"}</strong></p>
                                </div>
                            </div>

                            {/* Notes */}
                            <div className="mt-6">
                                <label className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                                <textarea
                                    className="w-full border border-gray-300 rounded-md p-2 text-sm"
                                    placeholder='Add a note...'
                                    defaultValue={data.general_notes || ""}
                                />
                            </div>

                            {/* Treatment Info */}
                            <div className="mt-4 text-sm text-gray-700 space-y-2">
                                <p>
                                    <span className="font-semibold">Treatment Option:</span>{' '}
                                    <span className="text-orange-500 cursor-pointer">{data.plan?.name}</span>
                                </p>
                                <p><span className="font-semibold">Treatment Expiration Date:</span> {data.plan?.expiration_date}</p>
                            </div>

                            <div>
                                <h2 className="text-md font-semibold my-2">Current Status</h2>
                            </div>
                            <div className="mt-2 p-2 rounded-[8px] bg-[#EDEEEE] flex justify-between items-center">
                                <span className="text-sm text-gray-700">Target Retainer</span>
                                <div className='flex justify-center items-center gap-2'>
                                    <button
                                        className="px-6 py-2 bg-orange-500 text-white text-sm rounded-full cursor-pointer"
                                        onClick={() => handleClick()}
                                    >
                                        View
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}

                    {activeTab === 'records' && <RecordsTab 
                    // data={data} 
                    />}

                    {activeTab === 'conversations' && <ConversationsTab />}
                </div>

                {/* Right Sticky Accordions - Always visible regardless of tab */}
                <div className="sticky top-6 h-fit">
                    <div className="bg-[#FFF6F0] p-4 rounded-xl shadow-lg">
                        <div className='flex items-center justify-between py-2'>
                            <p className="text-sm font-semibold text-gray-700">Supplemental order options</p>
                        </div>
                        <div className="pb-4 grid grid-cols-2 gap-2">
                            {allButtons.map((item, idx) => (
                                <button
                                    onClick={() => item.action()}
                                    key={idx}
                                    className="w-full text-center cursor-pointer text-xs text-gray-600 border-[#99999966] border-solid border-[1px] rounded-md py-5 px-3"
                                >
                                    {item.text}
                                </button>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default PatientFile;

