import { FieldValues } from "react-hook-form";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import { SpecialProps } from "./MovementResctiction_2";


const Overjet = <T extends FieldValues>({ register, errors, number }: SpecialProps<T>) => {

    return (
        <div>
            <p className="text-lg font-semibold text-gray-800">{`${number}`}{" "}Overjet</p>
            <div className="flex items-center gap-2">
                <RoundRadioButton
                    id="overjet-showResulting"
                    label="Show resulting overjet after alignment"
                    value="showResultingOverjet"
                    register={register}
                    name="overjet.option"
                    labelClass='!text-[#434343] text-base'
                />
                <RoundRadioButton
                    id="overjet-maintainInitial"
                    label="Maintain initial overjet (may require IPR)"
                    value="maintainInitialOverjet"
                    register={register}
                    name="overjet.option"
                    labelClass='!text-[#434343] text-base'
                />
                <RoundRadioButton
                    id="overjet-improveWithIPR"
                    label="Improve resulting overjet with IPR"
                    value="improveOverjetWithIPR"
                    register={register}
                    name="overjet.option"
                    labelClass='!text-[#434343] text-base'
                />
            </div>
            {errors.overjet?.message && (
                <p className="text-red-500 text-sm mt-1">{String(errors.overjet.message)}</p>
            )}
        </div>
    )
}

export default Overjet