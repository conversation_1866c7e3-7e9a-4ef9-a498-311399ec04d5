"use client"
import { useState } from "react";
import LongVersion from "./versions/LongVersion";
import ShortVersion from "./versions/ShortVersion";


const CasePrescription = () => {
    const [tab, setTab] = useState<"long" | "short">("long");
  
    return (
        <div>
            <div className="flex gap-4 mb-4">
                <button
                    className={`px-4 py-2 rounded ${tab === "long" ? "bg-primary text-white" : "bg-gray-200"}`}
                    onClick={() => setTab("long")}
                >
                    Long Version
                </button>
                <button
                    className={`px-4 py-2 rounded ${tab === "short" ? "bg-primary text-white" : "bg-gray-200"}`}
                    onClick={() => setTab("short")}
                >
                    Short Version
                </button>
            </div>
            {tab === "long" && <LongVersion />}
            {tab === "short" && <ShortVersion />}
        </div>
    );
};

export default CasePrescription