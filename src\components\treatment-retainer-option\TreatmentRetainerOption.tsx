'use client'
import { useRouter } from 'next/navigation';
import React, { useState } from 'react'
import FormWrapper from '../reuseable/FormWrapper';
import FormHeading from '../reuseable/FormHeading';
import CommenCard from '../reuseable/CommenCard';

type archType = {
    price: string;
    description: string;
}

type TreatmentOption = {
    value: string;
    label: string;
    stages: string;
    additionalAligners: string;
    archTypes: archType[]
};
const treatmentOptions: TreatmentOption[] = [
    {
        value: "4d-graphy-retainer",
        label: "4D Graphy Retainer",
        stages: "-",
        additionalAligners: "-",
        archTypes: [
            {
                price: "-",
                description: "dual arch"
            },
            {
                price: "-",
                description: "single arch"
            }
        ],
    }
];


const TreatmentRetainerOption = () => {
    const router = useRouter();
    const [treatmentOption, setTreatmentOption] = useState<string>('');

    const onSubmit = () => {
        console.log(treatmentOption)
    }
    const handleSelect = (_planid: string, value: string) => {
        setTreatmentOption(value)
        localStorage.setItem("treatmentOption", value)
        router.push("/retainer-info")
    }
    return (
        <FormWrapper classNames='!grid-cols-1' onSubmit={() => onSubmit()} onBack={() => router.back()} showNextButton={false}>
            <div className='col-span-1 flex flex-col flex-grow'>
                <FormHeading text='Select treatment option for Ali, Momen' classes='!text-2xl' />

                <div className='relative grid 2xl:grid-cols-3 xl:grid-cols-2 grid-cols-1 gap-4 p-8 !bg-primaryLight rounded-[10px]'>
                    {treatmentOptions.map((option: TreatmentOption, index: number) => {
                        return (
                            <CommenCard key={index} classes='col-span-1' header={option.label} buttonText='Select' selectedValue={treatmentOption} value={option.value} onClickButton={handleSelect} flexGrow={true} planid={option.value}>
                                <div className=''>
                                    <p className='text-primary font-semibold text-lg'>{option.label}</p>
                                    <p className='text-dark font-semibold text-lg'>{option.additionalAligners}</p>

                                    <div className='flex flex-col gap-2 mt-8 mb-2'>
                                        {option.archTypes.map((type: archType, index: number) => {
                                            return (
                                                <div key={index} className='flex justify-between gap-7'>
                                                    <p className='flex-grow text-dark'>{type.description}</p>
                                                    <p className='text-dark font-bold flex items-center'>{type.price}</p>
                                                </div>
                                            )
                                        })}
                                    </div>
                                </div>
                            </CommenCard>
                        )
                    })}
                </div>
            </div>

        </FormWrapper>
    )
}

export default TreatmentRetainerOption
