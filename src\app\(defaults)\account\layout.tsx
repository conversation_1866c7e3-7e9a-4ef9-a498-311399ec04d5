'use client'
import '@/app/globals.css';
import Header from '@/components/reuseable/Header';
import { useEffect, useState } from 'react';
import { usePathname, useRouter } from 'next/navigation';

export default function DefaultLayout({ children }: { children: React.ReactNode }) {
    const pathname = usePathname();
    const router = useRouter();
    const [activeTab, setActiveTab] = useState("");

    useEffect(() => {
        const pathParts = pathname.split('/');
        const lastPart = pathParts[pathParts.length - 1];
        setActiveTab(lastPart.charAt(0).toUpperCase() + lastPart.slice(1));
    }, [pathname]);

    return (
        <div className="p-4">
            <Header onSearchChange={() => { }} searchValue={""} />
            
        {["Summary", "Payments", "Dr-profile", "Staff"].includes(activeTab) &&
            (    <div className="space-x-4 px-3">
                <button
                    onClick={() => router.push('/account/summary')}
                    className={`p-4 rounded-full cursor-pointer ${activeTab === "Summary" ? "bg-[#EB6309] text-white" : "bg-[#FFF]"}`}
                >
                    Summary
                </button>
                {/* <button
                    onClick={() => router.push('/account/payments')}
                    className={`p-4 rounded-full cursor-pointer ${activeTab === "Payments" ? "bg-[#EB6309] text-white" : "bg-[#FFF]"}`}
                >
                    Payments
                </button> */}
                <button
                    onClick={() => router.push('/account/dr-profile')}
                    className={`p-4 rounded-full cursor-pointer ${activeTab === "Dr-profile" ? "bg-[#EB6309] text-white" : "bg-[#FFF]"}`}
                >
                    Dr. Profile
                </button>
                <button
                    onClick={() => router.push('/account/staff')}
                    className={`p-4 rounded-full cursor-pointer ${activeTab === "Staff" ? "bg-[#EB6309] text-white" : "bg-[#FFF]"}`}
                >
                    Staff
                </button>
            </div>)
        }
            <div className="p-4">{children}</div>
        </div>
    );
}
