import { FieldError, Merge } from "react-hook-form";
import { z } from "zod";

export const stepOneSchema = z
  .object({
    option: z.string().nonempty('Please select an arch to treat.'),
    value: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (data.option === 'Upper' && !data.value) {
      ctx.addIssue({
        path: ['value'],
        code: z.ZodIssueCode.custom,
        message: 'Please select an option',
      });
    }

    if (data.option === 'Lower' && !data.value) {
      ctx.addIssue({
        path: ['value'],
        code: z.ZodIssueCode.custom,
        message: 'Please select an option',
      });
    }
  });


export const movementRestrictionSchema = z.object({
  option: z.enum(['none', 'specific'], {
    required_error: 'Please select a teeth movement restriction',
  }),
  restrictedTeeth: z.array(z.string()),
  primaryDefinationTeeth: z.array(z.string()),
}).superRefine((data, ctx) => {
  if (data.option === 'specific' && data.restrictedTeeth.length === 0) {
    ctx.addIssue({
      path: ['restrictedTeeth'],
      code: z.ZodIssueCode.custom,
      message: 'Please specify the teeth that should not be moved.',
    });
  }
});

export const attachmentRestrictionSchema = z.object({
  option: z.enum(['none', 'specific'], {
    required_error: 'Please select an attachment option',
  }),
  restrictedTeeth: z.array(z.string()),
  primaryDefinationTeeth: z.array(z.string()),
}).superRefine((data, ctx) => {
  if (data.option === 'specific' && data.restrictedTeeth.length === 0) {
    ctx.addIssue({
      path: ['restrictedTeeth'],
      code: z.ZodIssueCode.custom,
      message: 'Please specify the teeth where attachments should not be placed.',
    });
  }
});

export const ExtractionSchema = z.object({
  option: z.enum(['none', 'extraction', 'later'], {
    required_error: 'Please select an option',
  }),
  extractionTeeth: z.array(z.string()),
  primaryDefinationTeeth: z.array(z.string()),
}).superRefine((data, ctx) => {
  if (data.option === 'extraction' && data.extractionTeeth.length === 0) {
    ctx.addIssue({
      path: ['extractionTeeth'],
      code: z.ZodIssueCode.custom,
      message: 'Please specify the teeth',
    });
  }
});


const improveCanineTreatmentOptionsSchema = z.object({
  ipr: z.string().optional(),
  class23Elastic: z.string().optional(),
  molarDistalization: z.string().optional(),
  MIATADAssociatedElastic: z.string().optional(),
  molarMesialization: z.string().optional(),
});

const sagittalRelationshipSideSchema = z.object({
  option: z.string().optional(),
  decideLaterNote: z.string().optional(),
  improveCanine: z.string().optional(),
  improveCanineOption: z.string().optional(),
  improveMolar: z.string().optional(),
  improveMolarOption: z.string().optional(),
  improveCanineNote: z.string().optional(),
  improveCanineTreatmentOptions: improveCanineTreatmentOptionsSchema.optional(),
  improveMolarNote: z.string().optional(),
  improveMolarTreatmentOptions: improveCanineTreatmentOptionsSchema.optional(),
});

export const sagittalRelationship = z.object({
  right: sagittalRelationshipSideSchema,
  left: sagittalRelationshipSideSchema,
})
  .superRefine((data, ctx) => {
    if (!data.right.option && !data.right.improveCanine && !data.right.improveMolar) {
      ctx.addIssue({
        path: ['right', 'option'],
        code: z.ZodIssueCode.custom,
        message: 'Please select an option',
      });
    }

    // Custom validation for left side
    if (!data.left.option && !data.left.improveCanine && !data.left.improveMolar) {
      ctx.addIssue({
        path: ['left', 'option'],
        code: z.ZodIssueCode.custom,
        message: 'Please select an option or check',
      });
    }
  });;



export const overjetSchema = z.object({
  option: z.enum(
    [
      'showResultingOverjet',
      'maintainInitialOverjet',
      'improveOverjetWithIPR',
    ],
    {
      required_error: 'Please select an overjet option.',
      invalid_type_error: 'Please select an overjet option.',
    }
  ),
});

const upperSchama = z.object({
  options: z.array(z.string()).optional(),
  others: z.boolean().optional(),
  otherNote: z.string().optional(),
  anteriorBiteRamps: z.array(z.string()).optional()
})

export const overbiteSchema = z.object({
  option: z.string().min(1, "Please select an option"),
  upper: upperSchama,
  lower: upperSchama
})

export const biteRamps = z.object({
  option: z.string(),
  selectedTooth: z.array(z.enum(["incisors", "canines"])).optional(), // <-- array of strings
  incisorOption: z.object({
    central: z.boolean().optional(),
    lateral: z.boolean().optional(),
  }).optional(),
})


export const midline = z.object({
  option: z.enum([
    "showResultingMidline",
    "maintainInitialMidline",
    "improveMidlineWithIPR"
  ], { errorMap: () => ({ message: "Please select a midline option." }) }),
  midlineIPRUpper: z.boolean().optional(),
  midlineIPRLower: z.boolean().optional(),
  upperMidlineShift: z.string().optional(),
  lowerMidlineShift: z.string().optional(),
}).superRefine((data, ctx) => {
  if (data.option === "improveMidlineWithIPR") {
    if (!data.midlineIPRUpper && !data.midlineIPRLower) {
      ctx.addIssue({
        path: ["midlineIPRUpper"],
        code: z.ZodIssueCode.custom,
        message: "Select at least one (Upper or Lower)",
      });
      ctx.addIssue({
        path: ["midlineIPRLower"],
        code: z.ZodIssueCode.custom,
        message: "Select at least one (Upper or Lower)",
      });
    }
    if (data.midlineIPRUpper && !data.upperMidlineShift) {
      ctx.addIssue({
        path: ["upperMidlineShift"],
        code: z.ZodIssueCode.custom,
        message: "Select upper midline shift direction",
      });
    }
    if (data.midlineIPRLower && !data.lowerMidlineShift) {
      ctx.addIssue({
        path: ["lowerMidlineShift"],
        code: z.ZodIssueCode.custom,
        message: "Select lower midline shift direction",
      });
    }
  }
});


export type ResolveField = {
  label: string;
  name: keyof ResolveLowerFormValues;
   value: string | string[];
  error?: FieldError | Merge<FieldError, (FieldError | undefined)[]>;
  selected?: string;
};

export type ResolveLowerFormValues = {
  expand: string;
  procline: string;
  iprAnterior: string;
  molarDistalization?: [];
};

export const spacingSchema = z.object({
  option: z.enum([
    "Decide after 3D Simulation",
    "Close all spaces",
    "Leave spaces"
  ], { errorMap: () => ({ message: "Select a spacing option" }) }),
  note: z.string().optional(),
  upper: z.string().optional(),
  lower: z.string().optional(),
  resolveUpper: z.object({
    expand: z.enum(["primarily", "asNeeded", "none"]).optional(),
    procline: z.enum(["primarily", "asNeeded", "none"]).optional(),
    iprAnterior: z.enum(["primarily", "asNeeded", "none"]).optional(),
    molarDistalization: z.array(z.string()).optional(),
  }).optional(),
  resolveLower: z.object({
    expand: z.enum(["primarily", "asNeeded", "none"]).optional(),
    procline: z.enum(["primarily", "asNeeded", "none"]).optional(),
    iprAnterior: z.enum(["primarily", "asNeeded", "none"]).optional(),
    molarDistalization: z.array(z.string()).optional(),
  }).optional(),
}).superRefine((data, ctx) => {
  if (data.option === "Close all spaces") {
    if (!data.upper) {
      ctx.addIssue({
        path: ["upper"],
        code: z.ZodIssueCode.custom,
        message: "Select an upper option",
      });
    }
    if (!data.lower) {
      ctx.addIssue({
        path: ["lower"],
        code: z.ZodIssueCode.custom,
        message: "Select a lower option",
      });
    }
  }
  if (data.option === "Leave spaces" && (!data.note || data.note.trim() === "")) {
    ctx.addIssue({
      path: ["note"],
      code: z.ZodIssueCode.custom,
      message: "Please enter a note",
    });
  }
});

export const specialNoteSchema = z.object({
  note: z.string({ required_error: "Please Enter special instructions" }),
}).superRefine((data, ctx) => {
  if (!data.note) {
    ctx.addIssue({
      path: ['note'],
      code: z.ZodIssueCode.custom,
      message: 'Please Enter special instructions',
    });
  };
})
export const IPRAttachmentsSchema = z.object({
  option: z.string({ required_error: "Please Select an option" }),
  note: z.string({ required_error: "Please Select an interval" }).optional()
}).superRefine((data, ctx) => {
  if (!data.option) {
    ctx.addIssue({
      path: ['option'],
      code: z.ZodIssueCode.custom,
      message: 'Please Select an option',
    });
  };
  if (data.option == "notSure" && !data.note) {
    ctx.addIssue({
      path: ['note'],
      code: z.ZodIssueCode.custom,
      message: 'Please write a note',
    });
  };
})
export const PosteriorCrossBiteSchema = z.object({
  option: z.string(),
  location: z
    .array(z.enum(["right", "left"]))
    .optional(),
}).superRefine((data, ctx) => {
  if (data.option == "correct" && !data.location) {
    ctx.addIssue({
      path: ['location'],
      code: z.ZodIssueCode.custom,
      message: 'Please select an option',
    });
  };
})
export const AnteriorEdgeToEdgeSchema = z.object({
  option: z.string({ required_error: "Please Select an option" }).optional(),
  extrudeAnteriors: z.string().optional(),
}).superRefine((data, ctx) => {
  if (data.option == "correct" && !data.extrudeAnteriors) {
    ctx.addIssue({
      path: ['extrudeAnteriors'],
      code: z.ZodIssueCode.custom,
      message: 'Please select an option',
    });
  };
})

export const AnteriorCrossBiteSchema = z.object({
  option: z.string(),
  location: z
    .array(z.enum(["ProtractUpperAnterior", "RetractLowerAnterior"]))
    .optional(),
}).superRefine((data, ctx) => {
  if ((data.option === "correct" || data.option === "improve") && (!data.location || data.location.length === 0)) {
    ctx.addIssue({
      path: ['location'],
      code: z.ZodIssueCode.custom,
      message: 'Please select at least one option',
    });
  }
});


export const TeethInformationSchema = z.object({
  missingTeethOption: z.enum(['none', 'missingTeeth'], {
    required_error: 'Please select an option',
  }),
  primaryDefinationOption: z.enum(['none', 'primaryDefinition'], {
    required_error: 'Please select an option',
  }),
  missingTeeth: z.array(z.string()),
  primaryDefinationTeeth: z.array(z.string()),
}).superRefine((data, ctx) => {
  if (data.missingTeethOption === 'missingTeeth' && data.missingTeeth.length === 0) {
    ctx.addIssue({
      path: ['missingTeeth'],
      code: z.ZodIssueCode.custom,
      message: 'Please specify the teeth.',
    });
  }
  if (data.primaryDefinationOption === 'primaryDefinition' && data.primaryDefinationTeeth.length === 0) {
    ctx.addIssue({
      path: ['primaryDefinationTeeth'],
      code: z.ZodIssueCode.custom,
      message: 'Please specify the teeth.',
    });
  }
});