import React from 'react'
import { UseFormRegisterReturn } from 'react-hook-form'

interface CustomInputProps {
  type: string
  placeholder: string
  error?: string
  register: UseFormRegisterReturn
  className?: string
  containerClassName?: string,
  min?: number,
  max?: number,
  maxLength?: number;
  onInput?: (e: React.FormEvent<HTMLInputElement>) => void;
}

const CustomNumberInput: React.FC<CustomInputProps> = ({ type, placeholder, error, register, className, containerClassName, min, max, onInput, maxLength }) => {
  return (
    <div className={`${containerClassName}  flex justify-center items-center px-4 py-3.5 border ${error ? 'border-red-500' : 'border-gray/40 '} rounded-[60px] outline-none`}>
      <input
        min={min}
        max={max}
        type={type}
        {...register}
        placeholder={placeholder}
        maxLength={maxLength}
        className={`${className} focus:outline-none text-center`}
        onInput={onInput}
      />
      {error && <p className="text-red-500 text-sm mt-1 ps-3">{error}</p>}
    </div>
  )
}

export default CustomNumberInput