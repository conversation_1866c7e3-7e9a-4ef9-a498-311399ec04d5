'use client'
import React, { useEffect } from 'react'
import FormWrapper from '../reuseable/FormWrapper'
import { useRouter } from 'next/navigation'
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, SubmitHandler } from "react-hook-form";
import StepsCheckBoxRegister from "../reuseable/SquareCheckBoxRegister";
import CustomTextarea from '../reuseable/CustomTextArea';
import FormHeading from '../reuseable/FormHeading';

// 1. Define Zod schema
const retainerSchema = z.object({
    sets: z.number().min(1, { message: "Number of sets must be at least 1" }),
    archRequired: z.array(z.enum(["upper", "lower"])).optional(),
    notes: z.string().optional(),
});

type RetainerFormValues = z.infer<typeof retainerSchema>;

const RetainerInfo = () => {
    const router = useRouter()

    // 2. useForm hook with zod resolver
    const {
        register,
        handleSubmit,
        watch,
        formState: { errors },
        setValue,
    } = useForm<RetainerFormValues>({
        resolver: zodResolver(retainerSchema),
        defaultValues: {
            sets: 1,
            archRequired: [],
            notes: '',
        },
    });

    // Watch form values
    const sets = watch("sets");
    const archRequired = watch("archRequired");
    console.log("🚀 ~ RetainerInfo ~ archRequired:", archRequired)
    const notes = watch("notes");
    console.log("🚀 ~ RetainerInfo ~ notes:", notes)

    // 3. Form submission handler
    const onSubmit: SubmitHandler<RetainerFormValues> = (data) => {
        console.log("Form Data:", data);
        localStorage.setItem("retainerFormData", JSON.stringify(data));
        router.push("/patient-retainer-record"); 
    };

    useEffect(() => {
        const storedData = localStorage.getItem("retainerFormData");
        if (storedData) {
            const parsedData = JSON.parse(storedData);
            setValue("sets", parsedData.sets);
            setValue("archRequired", parsedData.archRequired);
            setValue("notes", parsedData.notes);
        }
    }, [setValue]);

    return (
        <FormWrapper
            classNames="!grid-cols-1"
            onSubmit={handleSubmit(onSubmit)}
            onBack={() => router.back()}
        >
            <div className="col-span-1 flex flex-col flex-grow min-h-[550px]">
                {/* Number of sets */}
                <div className="mb-4">
                    <label className="block font-semibold text-lg mb-2">Number of sets<span className="text-red-500">*</span></label>
                    <div className="flex items-center gap-2">
                        <button type="button" onClick={() => setValue("sets", Math.max(1, sets - 1))} className="px-2 py-1 border rounded">-</button>
                        <span>{sets} sets</span>
                        <button type="button" onClick={() => setValue("sets", sets + 1)} className="px-2 py-1 border rounded">+</button>
                        <span className="ml-2 text-xs text-gray-500">(Additional retainers require extra fees)</span>
                    </div>
                    {errors.sets && <p className="text-red-500 text-sm">{errors.sets.message}</p>}
                </div>

                {/* Arch Required */}
                <div className="mb-4">
                    <label className="block font-semibold text-lg mb-2">Arch Required<span className="text-red-500">*</span></label>
                    <div className="flex gap-4">
                        <StepsCheckBoxRegister
                            id={`upper`}
                            label="upper"
                            value="upper"
                            register={register("archRequired")}
                            name={`archRequired`}
                            labelClass="!text-dark text-base"
                            rootLableClassName="!flex-row"
                        />
                        <StepsCheckBoxRegister
                            id={`lower`}
                            label="lower"
                            value="lower"
                            register={register("archRequired")}
                            name={`archRequired`}
                            labelClass="!text-dark text-base"
                            rootLableClassName="!flex-row"
                        />
                    </div>
                </div>

                {/* Additional Notes */}
                <div className="mb-4">
                    <FormHeading text="Additional notes:" />
                    <CustomTextarea className="px-4 !py-4" error={errors.notes?.message} register={register("notes")} rows={8} />
                    
                </div>
            </div>
        </FormWrapper>
    )
}

export default RetainerInfo