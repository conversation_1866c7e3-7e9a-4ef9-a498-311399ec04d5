import { render, screen, fireEvent } from '@testing-library/react'
import Login from './Login'

describe('Login Component', () => {
  it('renders the login form', () => {
    render(<Login />)
    expect(screen.getByPlaceholderText('ID')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Password')).toBeInTheDocument()
    expect(screen.getByText('Login')).toBeInTheDocument()
  })

  it('shows validation errors when fields are empty', async () => {
    render(<Login />)
    fireEvent.click(screen.getByText('Login'))
    expect(await screen.findByText('ID is required')).toBeInTheDocument()
    expect(await screen.findByText('Password is required')).toBeInTheDocument()
  })
})