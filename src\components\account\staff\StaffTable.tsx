"use client";

import Pagination from "@/components/reuseable/Pagination";
import React, { useState, useEffect } from "react";
import { updateEmployeeStatus } from "@/utils/ApisHelperFunction";
import getAndDecryptCookie from "@/app/lib/auth";
import { toast } from "react-toastify";
import { Listbox } from "@headlessui/react";

// Define the structure for staff data
export interface StaffMember {
  id: string;
  name: string;
  accessLevel: string;
  email: string;
  phone?: string;
  profession?: string;
  status: "active" | "inactive";
}

interface StaffTableProps {
  data: StaffMember[];
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  totalPages?: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (items: number) => void;
  onViewStaff: (id: string) => void;
  onEditStaff: (id: string) => void;
  onStatusChange?: (id: string, status: "active" | "inactive") => void;
  onSearch?: (searchTerm: string) => void;
}

const statusOptions = [
  { value: "active", label: "Active", color: "bg-green-100 text-green-800" },
  { value: "inactive", label: "Inactive", color: "bg-red-100 text-red-800" },
];

const StaffTable: React.FC<StaffTableProps> = ({
  data,
  currentPage,
  totalItems,
  itemsPerPage,
  totalPages,
  onPageChange,
  onItemsPerPageChange,
  onViewStaff,
  onEditStaff,
  onStatusChange,
  onSearch,
}) => {
  // State for search functionality
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredData, setFilteredData] = useState<StaffMember[]>(data);
  // Loading state for status update
  const [statusLoadingId, setStatusLoadingId] = useState<string | null>(null);

  // Handler for status change
  const handleStatusChange = async (
    e: React.ChangeEvent<HTMLSelectElement>,
    staffId: string
  ) => {
    e.stopPropagation(); // Prevent row click
    const newStatus = e.target.value as "active" | "inactive";
    setStatusLoadingId(staffId);
    const token = getAndDecryptCookie("AccessToken");
    if (!token) {
      toast.error("Authentication token missing. Please log in again.");
      setStatusLoadingId(null);
      return;
    }
    try {
      const result = await updateEmployeeStatus(staffId, newStatus, token);
      if (result && result.success) {
        toast.success(result.message || "Status updated successfully");
        // Update local data (if parent does not handle it)
        if (!onSearch) {
          setFilteredData((prev) =>
            prev.map((staff) =>
              staff.id === staffId ? { ...staff, status: newStatus } : staff
            )
          );
        }
        // If parent handles, call the callback
        onStatusChange?.(staffId, newStatus);
      } else {
        toast.error(result?.message || "Failed to update status");
      }
    } catch (error) {
      let errorMsg = "An unexpected error occurred.";
      if (typeof error === "object" && error !== null) {
        if ("message" in error && typeof (error as any).message === "string") {
          errorMsg = (error as any).message;
        } else if (
          "response" in error &&
          (error as any).response?.data?.message
        ) {
          errorMsg = (error as any).response.data.message;
        } else {
          errorMsg = JSON.stringify(error);
        }
      } else if (typeof error === "string") {
        errorMsg = error;
      }
      toast.error(errorMsg);
    } finally {
      setStatusLoadingId(null);
    }
  };

  // Handler for search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);

    // If parent component handles search, call the onSearch prop
    if (onSearch) {
      onSearch(value);
    } else {
      // Otherwise, filter locally
      filterData(value);
    }
  };

  // Function to filter data locally
  const filterData = (term: string) => {
    if (!term.trim()) {
      setFilteredData(data);
      return;
    }

    const filtered = data.filter(
      (staff) =>
        staff.name.toLowerCase().includes(term.toLowerCase()) ||
        staff.email.toLowerCase().includes(term.toLowerCase()) ||
        staff.phone?.toLowerCase().includes(term.toLowerCase()) ||
        staff.profession?.toLowerCase().includes(term.toLowerCase()) ||
        staff.accessLevel.toLowerCase().includes(term.toLowerCase())
    );

    setFilteredData(filtered);
  };

  // Update filtered data when data prop changes
  useEffect(() => {
    if (searchTerm) {
      filterData(searchTerm);
    } else {
      setFilteredData(data);
    }
  }, [data]);

  // Determine which data to display: filtered or original
  const displayData = onSearch ? data : filteredData;

  return (
    <div className="w-full">
      {/* Search Field */}
      <div className="mb-4 flex items-center justify-end">
        <div className="relative w-3xs">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg
              className="w-4 h-4 text-gray-500"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 20 20"
            >
              <path
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
              />
            </svg>
          </div>
          <input
            type="text"
            className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary focus:border-primary block w-full pl-10 p-2.5"
            placeholder="Search staff by name, email,"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Staff Name
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Access Level
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Email
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Phone
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Profession
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Status
              </th>
              <th
                scope="col"
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {displayData.length > 0 ? (
              displayData.map((staff) => (
                <tr
                  key={staff.id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => onViewStaff(staff.id)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {staff.name}
                        </div>
                        {/* <div className="text-sm text-gray-500">ID: {staff.id}</div> */}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {staff.accessLevel}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{staff.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{staff.phone}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {staff.profession}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div
                      className="relative w-full"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Listbox
                        value={staff.status}
                        onChange={async (newStatus: "active" | "inactive") => {
                          setStatusLoadingId(staff.id);
                          const token = getAndDecryptCookie("AccessToken");
                          if (!token) {
                            toast.error(
                              "Authentication token missing. Please log in again."
                            );
                            setStatusLoadingId(null);
                            return;
                          }
                          try {
                            const result = await updateEmployeeStatus(
                              staff.id,
                              newStatus,
                              token
                            );
                            if (result && result.success) {
                              toast.success(
                                result.message || "Status updated successfully"
                              );
                              if (!onSearch) {
                                setFilteredData((prev) =>
                                  prev.map((s) =>
                                    s.id === staff.id
                                      ? { ...s, status: newStatus }
                                      : s
                                  )
                                );
                              }
                              onStatusChange?.(staff.id, newStatus);
                            } else {
                              toast.error(
                                result?.message || "Failed to update status"
                              );
                            }
                          } catch (error) {
                            let errorMsg = "An unexpected error occurred.";
                            if (typeof error === "object" && error !== null) {
                              if (
                                "message" in error &&
                                typeof (error as any).message === "string"
                              ) {
                                errorMsg = (error as any).message;
                              } else if (
                                "response" in error &&
                                (error as any).response?.data?.message
                              ) {
                                errorMsg = (error as any).response.data.message;
                              } else {
                                errorMsg = JSON.stringify(error);
                              }
                            } else if (typeof error === "string") {
                              errorMsg = error;
                            }
                            toast.error(errorMsg);
                          } finally {
                            setStatusLoadingId(null);
                          }
                        }}
                        disabled={statusLoadingId === staff.id}
                      >
                        <div className="relative">
                          <Listbox.Button
                            className={`pl-4 pr-10 py-2 text-xs rounded-full  outline-none w-full appearance-none flex items-center justify-between transition-colors
                                                            ${
                                                              staff.status ===
                                                              "active"
                                                                ? "bg-green-100 text-green-800 "
                                                                : "bg-red-100 text-red-800 "
                                                            }
                                                            ${
                                                              statusLoadingId ===
                                                              staff.id
                                                                ? "opacity-50 cursor-not-allowed"
                                                                : ""
                                                            }
                                                        `}
                          >
                            <span>
                              {
                                statusOptions.find(
                                  (opt) => opt.value === staff.status
                                )?.label
                              }
                            </span>
                            <span className="absolute inset-y-0 right-3 flex items-center pointer-events-none">
                              <svg
                                className="w-4 h-4 text-gray-500"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  d="M19 9l-7 7-7-7"
                                />
                              </svg>
                            </span>
                            {statusLoadingId === staff.id && (
                              <span className="absolute inset-y-0 right-8 flex items-center">
                                <svg
                                  className="animate-spin h-4 w-4 text-primary"
                                  xmlns="http://www.w3.org/2000/svg"
                                  fill="none"
                                  viewBox="0 0 24 24"
                                >
                                  <circle
                                    className="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    strokeWidth="4"
                                  ></circle>
                                  <path
                                    className="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8v8z"
                                  ></path>
                                </svg>
                              </span>
                            )}
                          </Listbox.Button>
                          <Listbox.Options className="absolute z-10 mt-1 w-full rounded-2xl bg-white shadow-lg ring-1 ring-[#ffe5d4] ring-opacity-5 focus:outline-none">
                            {statusOptions.map((option) => (
                              <Listbox.Option
                                key={option.value}
                                value={option.value}
                                disabled={statusLoadingId === staff.id}
                                className={({
                                  active,
                                  selected,
                                }: {
                                  active: boolean;
                                  selected: boolean;
                                }) =>
                                  `cursor-pointer select-none relative py-2 pl-4 pr-10 rounded-2xl transition
                                                                    ${
                                                                      active
                                                                        ? "bg-primary/10 text-primary"
                                                                        : ""
                                                                    }
                                                                    ${
                                                                      selected
                                                                        ? option.color +
                                                                          " font-bold"
                                                                        : "text-gray-900"
                                                                    }
                                                                    `
                                }
                              >
                                {option.label}
                              </Listbox.Option>
                            ))}
                          </Listbox.Options>
                        </div>
                      </Listbox>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      title="Edit staff"
                      className="text-primary hover:text-primary-dark mr-3 cursor-pointer "
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent row click
                        onEditStaff(staff.id);
                      }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                      >
                        <path
                          d="M17.3287 7.26797L18.5487 6.04797C19.8157 4.78097 19.8157 2.71897 18.5487 1.45147C17.9352 0.838465 17.1197 0.501465 16.2502 0.501465C15.3807 0.501465 14.5647 0.838965 13.9517 1.45196L12.7322 2.67147L17.3287 7.26797ZM11.6717 3.73197L2.63723 12.7665C2.44473 12.959 2.29823 13.1965 2.21323 13.454L0.538232 18.5145C0.448732 18.7835 0.519232 19.08 0.719732 19.2805C0.863232 19.4235 1.05423 19.5 1.25023 19.5C1.32923 19.5 1.40873 19.4875 1.48623 19.462L6.54523 17.7865C6.80373 17.7015 7.04173 17.555 7.23423 17.362L16.2682 8.32797L11.6717 3.73197Z"
                          fill="#EB6309"
                        />
                      </svg>
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                  No staff members found matching your search.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination with proper props */}
      <Pagination
        currentPage={currentPage}
        totalItems={onSearch ? totalItems : filteredData.length}
        itemsPerPage={itemsPerPage}
        totalPages={totalPages || 0}
        onPageChange={onPageChange}
        onItemsPerPageChange={onItemsPerPageChange}
      />
    </div>
  );
};

export default StaffTable;
