'use client';

import Pagination from '@/components/reuseable/Pagination';
import React, { useState, useEffect } from 'react';

// Define the structure for staff data
export interface StaffMember {
    id: string;
    name: string;
    accessLevel: string;
    email: string;
    // phone: string;
    status: 'active' | 'inactive';
}

interface StaffTableProps {
    data: StaffMember[];
    currentPage: number;
    totalItems: number;
    itemsPerPage: number;
    onPageChange: (page: number) => void;
    onItemsPerPageChange: (items: number) => void;
    onViewStaff: (id: string) => void;
    onEditStaff: (id: string) => void;
    onStatusChange?: (id: string, status: 'active' | 'inactive') => void;
    onSearch?: (searchTerm: string) => void; // Add this prop if parent handles search
}

const StaffTable: React.FC<StaffTableProps> = ({
    data,
    currentPage,
    totalItems,
    itemsPerPage,
    onPageChange,
    onItemsPerPageChange,
    onViewStaff,
    onEditStaff,
    onStatusChange,
    onSearch
}) => {
    // State for search functionality
    const [searchTerm, setSearchTerm] = useState('');
    const [filteredData, setFilteredData] = useState<StaffMember[]>(data);

    // Handler for status change
    const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>, staffId: string) => {
        e.stopPropagation(); // Prevent row click
        const newStatus = e.target.value as 'active' | 'inactive';
        onStatusChange?.(staffId, newStatus);
    };

    // Handler for search input change
    const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearchTerm(value);
        
        // If parent component handles search, call the onSearch prop
        if (onSearch) {
            onSearch(value);
        } else {
            // Otherwise, filter locally
            filterData(value);
        }
    };

    // Function to filter data locally
    const filterData = (term: string) => {
        if (!term.trim()) {
            setFilteredData(data);
            return;
        }

        const filtered = data.filter(staff => 
            staff.name.toLowerCase().includes(term.toLowerCase()) ||
            staff.email.toLowerCase().includes(term.toLowerCase()) ||
            // staff.phone.toLowerCase().includes(term.toLowerCase()) ||
            staff.accessLevel.toLowerCase().includes(term.toLowerCase())
        );
        
        setFilteredData(filtered);
    };

    // Update filtered data when data prop changes
    useEffect(() => {
        if (searchTerm) {
            filterData(searchTerm);
        } else {
            setFilteredData(data);
        }
    }, [data]);

    // Determine which data to display: filtered or original
    const displayData = onSearch ? data : filteredData;

    return (
        <div className="w-full">
            {/* Search Field */}
            <div className="mb-4 flex items-center justify-end">
                <div className="relative w-3xs">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <svg 
                            className="w-4 h-4 text-gray-500" 
                            aria-hidden="true" 
                            xmlns="http://www.w3.org/2000/svg" 
                            fill="none" 
                            viewBox="0 0 20 20"
                        >
                            <path 
                                stroke="currentColor" 
                                strokeLinecap="round" 
                                strokeLinejoin="round" 
                                strokeWidth="2" 
                                d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
                            />
                        </svg>
                    </div>
                    <input
                        type="text"
                        className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary focus:border-primary block w-full pl-10 p-2.5"
                        placeholder="Search staff by name, email,"
                        value={searchTerm}
                        onChange={handleSearchChange}
                    />
                </div>
            </div>

            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                        <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Staff Name
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Access Level
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Email
                            </th>
                            {/* <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Phone
                            </th> */}
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                        {displayData.length > 0 ? (
                            displayData.map((staff) => (
                                <tr
                                    key={staff.id}
                                    className="hover:bg-gray-50 cursor-pointer"
                                    onClick={() => onViewStaff(staff.id)}
                                >
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center">
                                            <div className="ml-4">
                                                <div className="text-sm font-medium text-gray-900">{staff.name}</div>
                                                {/* <div className="text-sm text-gray-500">ID: {staff.id}</div> */}
                                            </div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900">{staff.accessLevel}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900">{staff.email}</div>
                                    </td>
                                    {/* <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900">{staff.phone}</div>
                                    </td> */}
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div
                                            className="relative border-[1px] border-primary rounded-full cursor-pointer"
                                            onClick={(e) => e.stopPropagation()} // Prevent row click when clicking on this cell
                                        >
                                            <select
                                                value={staff.status}
                                                onChange={(e) => handleStatusChange(e, staff.id)}
                                                className={`pl-2 pr-5 py-1 text-xs rounded-full border outline-none ${staff.status === 'active'
                                                    ? 'bg-green-100 text-green-800 border-0'
                                                    : 'bg-red-100 text-red-800 border-0'
                                                    }`}
                                            >
                                                <option value="active">Active</option>
                                                <option value="inactive">Inactive</option>
                                            </select>
                                            <div className="absolute inset-y-0 right-0 top-1 flex items-center pr-1 pointer-events-none">
                                                <svg className="h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                                </svg>
                                            </div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button
                                            className="text-primary hover:text-primary-dark mr-3"
                                            onClick={(e) => {
                                                e.stopPropagation(); // Prevent row click
                                                onEditStaff(staff.id);
                                            }}
                                        >
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                <path d="M17.3287 7.26797L18.5487 6.04797C19.8157 4.78097 19.8157 2.71897 18.5487 1.45147C17.9352 0.838465 17.1197 0.501465 16.2502 0.501465C15.3807 0.501465 14.5647 0.838965 13.9517 1.45196L12.7322 2.67147L17.3287 7.26797ZM11.6717 3.73197L2.63723 12.7665C2.44473 12.959 2.29823 13.1965 2.21323 13.454L0.538232 18.5145C0.448732 18.7835 0.519232 19.08 0.719732 19.2805C0.863232 19.4235 1.05423 19.5 1.25023 19.5C1.32923 19.5 1.40873 19.4875 1.48623 19.462L6.54523 17.7865C6.80373 17.7015 7.04173 17.555 7.23423 17.362L16.2682 8.32797L11.6717 3.73197Z" fill="#EB6309" />
                                            </svg>
                                        </button>
                                    </td>
                                </tr>
                            ))
                        ) : (
                            <tr>
                                <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                                    No staff members found matching your search.
                                </td>
                            </tr>
                        )}
                    </tbody>
                </table>
            </div>

            {/* Pagination with proper props */}
            <Pagination
                currentPage={currentPage}
                totalItems={onSearch ? totalItems : filteredData.length}
                itemsPerPage={itemsPerPage}
                onPageChange={onPageChange}
                onItemsPerPageChange={onItemsPerPageChange}
                totalPages={0}
                />
        </div>
    );
};

export default StaffTable;