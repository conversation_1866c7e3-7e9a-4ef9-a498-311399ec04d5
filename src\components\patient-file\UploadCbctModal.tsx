import React, { useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { toast } from 'react-toastify';

interface UploadCbctModalProps {
  onClose: () => void;
}

// Define schema with nullable File type
const cbctSchema = z.object({
  cbctFile: z.instanceof(File, { message: "CBCT file is required" }),
  reason: z.string().min(1, "Please provide a reason for the CBCT"),
});

type CbctFormValues = z.infer<typeof cbctSchema>;

const UploadCbctModal: React.FC<UploadCbctModalProps> = ({ onClose }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [currentStep, setCurrentStep] = useState<1 | 2>(1);
  
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    trigger,
    formState: { errors },
  } = useForm<CbctFormValues>({
    resolver: zodResolver(cbctSchema),
    defaultValues: {
      reason: '',
    },
  });

  // Watch form values for summary
  const reason = watch('reason');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);
      setValue('cbctFile', file);
    }
  };

  const onSubmit = (data: CbctFormValues) => {
    console.log('CBCT upload data:', data);
    localStorage.setItem('cbctReason', data.reason);
    localStorage.setItem('cbctFileName', data.cbctFile.name);
    toast.success('CBCT file uploaded successfully!');
    onClose();
  };

  // Fix: Add e parameter and prevent event propagation
  const handleNextStep = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent default behavior
    e.stopPropagation(); // Stop event propagation
    
    // Validate form before proceeding to summary
    const isValid = await trigger();
    if (isValid) {
      setCurrentStep(2);
    }
  };

  const handleBackStep = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentStep(1);
  };

  // Prevent modal closing when clicking inside content
  const handleModalContentClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <div className="fixed inset-0 bg-black/20 z-50 flex items-center justify-center p-2" onClick={() => onClose()}>
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-xl h-[80%] overflow-y-auto" onClick={handleModalContentClick}>
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-800">
            Upload CBCT {currentStep === 2 && '- Summary'}
          </h2>
          <button
            type="button"
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Step indicator */}
        <div className="px-6 pt-6">
          <div className="flex items-center">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 1 ? 'bg-primary text-white' : 'bg-gray-200'}`}>
              1
            </div>
            <div className={`h-1 flex-1 ${currentStep >= 2 ? 'bg-primary' : 'bg-gray-200'}`}></div>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 2 ? 'bg-primary text-white' : 'bg-gray-200'}`}>
              2
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)}>
          {currentStep === 1 ? (
            <div className="p-6 space-y-6">
              {/* CBCT File Upload */}
              <div>
                <label className="block text-lg font-medium text-gray-800 mb-3">
                  Upload CBCT File <span className="text-red-500">*</span>
                </label>
                <div className="bg-gray-50 p-8 rounded-lg border border-dashed border-gray-300 flex flex-col items-center justify-center">
                  {!selectedFile ? (
                    <>
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                      <p className="text-sm text-gray-600 mb-1">Drag and drop your CBCT file here</p>
                      <p className="text-xs text-gray-500 mb-4">or</p>
                      <label className="px-4 py-2 bg-primary text-white rounded-full cursor-pointer hover:bg-primary-dark transition">
                        Browse Files
                        <input
                          type="file"
                          accept=".zip"
                          className="hidden"
                          onChange={handleFileChange}
                        />
                      </label>
                    </>
                  ) : (
                    <div className="flex flex-col items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-green-500 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <p className="text-sm font-medium text-gray-800 mb-1">{selectedFile.name}</p>
                      <p className="text-xs text-gray-500 mb-4">{(selectedFile.size / (1024 * 1024)).toFixed(2)} MB</p>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedFile(null);
                          setValue('cbctFile', undefined as unknown as File);
                        }}
                        className="text-sm text-red-500 hover:text-red-700"
                      >
                        Remove file
                      </button>
                    </div>
                  )}
                </div>
                {errors.cbctFile && <p className="mt-2 text-sm text-red-500">{errors.cbctFile.message}</p>}
              </div>

              {/* Reason for CBCT */}
              <div>
                <label className="block text-lg font-medium text-gray-800 mb-3">
                  Reason for CBCT <span className="text-red-500">*</span>
                </label>
                <textarea
                  {...register("reason")}
                  className="w-full border border-gray-300 rounded-lg p-4 h-32 focus:ring-2 focus:ring-primary focus:border-primary"
                  placeholder="Please describe why you need this CBCT scan..."
                />
                {errors.reason && <p className="mt-2 text-sm text-red-500">{errors.reason.message}</p>}
              </div>
            </div>
          ) : (
            // Summary Step
            <div className="p-6 space-y-6">
              <div className="bg-white p-5 rounded-lg border border-gray-200">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Request Summary</h3>
                
                {/* CBCT File Information */}
                <div className="mb-6">
                  <h4 className="text-md font-medium text-gray-700 mb-3 border-b pb-2">CBCT File</h4>
                  {selectedFile && (
                    <div className="flex items-start gap-3 bg-gray-50 p-3 rounded-lg">
                      <div className="flex-shrink-0">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-primary" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div>
                        <p className="font-medium text-gray-800">{selectedFile.name}</p>
                        <p className="text-sm text-gray-500">
                          {(selectedFile.size / (1024 * 1024)).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                  )}
                </div>
                
                {/* Reason Information */}
                <div>
                  <h4 className="text-md font-medium text-gray-700 mb-3 border-b pb-2">Reason for CBCT</h4>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-gray-800 whitespace-pre-wrap">{reason}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="border-t border-gray-200 p-6 flex justify-between">
            {currentStep === 2 ? (
              <button
                type="button"
                onClick={handleBackStep}
                className="px-6 py-2 cursor-pointer border border-gray-300 rounded-full text-gray-700 font-medium hover:bg-gray-50"
              >
                Back
              </button>
            ) : (
              <div />
            )}

            <div className="flex gap-4">
              <button
                type="button"
                onClick={onClose}
                className="px-6 py-2 border border-gray-300 rounded-full text-gray-700 font-medium cursor-pointer hover:bg-gray-50"
              >
                Cancel
              </button>
              
              {currentStep === 1 ? (
                <button
                  type="button"
                  onClick={handleNextStep}
                  className="px-6 py-2 bg-primary text-white rounded-full font-medium hover:bg-primary-dark cursor-pointer"
                >
                  Next
                </button>
              ) : (
                <button
                  type="submit"
                  className="px-6 py-2 bg-primary text-white rounded-full font-medium cursor-pointer hover:bg-primary-dark"
                >
                  Upload
                </button>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UploadCbctModal;