"use server";

// import { API_ROUTES } from "@/utils/ApiRoutes";
// import getAndDecryptCookie from "../lib/auth";
import { cookies } from "next/headers";
import CryptoJs from "crypto-js";

interface ApiResponse<T> {
  data?: T;
}

export async function getServerCookies(value: string): Promise<string> {
  const _key = process.env.NEXT_PUBLIC_ENCRYPTION_KEY!;

  const cookie = await cookies();
  const encryptedToken = cookie.get(value);

  if (encryptedToken?.value) {
    const decryptedToken = CryptoJs.AES?.decrypt(encryptedToken?.value, _key);
    return decryptedToken?.toString(CryptoJs.enc.Utf8) ?? "";
  }
  return "";
}

export const fetchApi = async <T>(url: string): Promise<T | null> => {
  try {
    const token = await getServerCookies("AccessToken");
    const headers = token ? { Authorization: `Bearer ${token}` } : undefined;
    const response = await fetch(url, {
      method: "GET",
      headers: {
        ...(headers || {}),
        cache: "no-cache",
        "cahe-control": "no-cache",
        "Content-Type": "application/json",
      },
    });
    const data: ApiResponse<T> = await response.json();
    if (response.status === 200) {
      return data.data || null;
    } else {
      return null;
    }
  } catch (error) {
    console.error("Error fetching data:", error);
    return null;
  }
};
