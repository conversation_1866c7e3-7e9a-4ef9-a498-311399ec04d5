'use client';

import Image from 'next/image';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import CustomInput from '../reuseable/CustomInput';
import logo from '../../../public/images/logo1.png';
import { toast } from 'react-toastify';
import { useRouter } from 'next/navigation';
import { forgetPassword } from '@/utils/ApisHelperFunction'; // Import the helper function

// Define Zod schema to accept either email or username
const forgetPasswordSchema = z.object({
    identifier: z.string()
        .min(1, "Email or username is required")
});

type ForgetPasswordFormValues = z.infer<typeof forgetPasswordSchema>;

const Forgetpassword = () => {
    const navigate = useRouter();
    const [isSubmitting, setIsSubmitting] = useState(false);

    const {
        register,
        handleSubmit,
        formState: { errors },
    } = useForm<ForgetPasswordFormValues>({
        resolver: zodResolver(forgetPasswordSchema),
        defaultValues: {
            identifier: '',
        },
    });

    const onSubmit = async (data: ForgetPasswordFormValues) => {
        const response = await forgetPassword(data.identifier);
        try {
            setIsSubmitting(true);
            if (response && response.success) {
                toast.success(response.message);
                navigate.push('/login');
                console.log('Forget password request successful:', response);
            } else if (response) {
                toast.error(response.message || 'Please try again.');
                console.error('Forget password error:', response);
            } else {
                toast.error('No response from server. Please try again.');
                console.error('Forget password error: No response');
            }
        } catch (error) {
            toast.error(response?.message || 'An error occurred.');
            console.error('Forget password error:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <>
            <div className="min-h-screen authbackground bg-cover bg-center bg-no-repeat flex items-center justify-center relative overflow-hidden">
                {/* Login Box */}
                <div className="w-[30%] max-md:w-[80%] md:w-[50%] lg:w-[40%] xl:w-[30%] 2xl:w-[29%] z-30">
                    <div className="xl:mb-8 lg:mb-5 2xl:mb-14">
                        <Image src={logo} alt="Aligners Logo" className="mx-auto mb-2 w-44 xl:w-52" width={1000} height={1000} />
                    </div>
                    <div className="z-10 w-full 2xl:p-[50px] lg:p-[30px] p-[20px] rounded-[40px] bg-[#4444430F] text-center border border-[#44444321] backdrop-blur-sm">
                        <h1 className="text-[38px] max-sm:text-[26px] max-md:text-[28px] md:text-[32px] font-bold text-[#444443] leading-normal">
                            Forgot Password
                        </h1>
                        <p className="text-sm text-gray-600 mt-2">
                            Enter your email or username below to receive password reset instructions.
                        </p>
                        <form onSubmit={handleSubmit(onSubmit)} className="text-left pt-4">
                            <div className="space-y-2">
                                <CustomInput
                                    className='!px-5 !py-[16px] !2xl:py-[18px] lg:py-3'
                                    type="text"
                                    placeholder="Email or Username"
                                    register={register('identifier')}
                                    error={errors.identifier?.message}
                                />
                            </div>
                            <div className="pt-5">
                                <button
                                    type="submit"
                                    disabled={isSubmitting}
                                    className={`w-full py-4 lg:py-3 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition cursor-pointer font-medium ${isSubmitting ? 'opacity-70' : ''}`}
                                >
                                    {isSubmitting ? 'Submitting...' : 'Submit'}
                                </button>
                            </div>
                        </form>

                        <div className="xl:mt-[8px] 2xl:mt-[14px]">
                            <button
                                className="text-sm text-[#F00] hover:underline cursor-pointer"
                                onClick={() => navigate.push('/login')}
                            >
                                Go to Login Page
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div className="absolute bottom-1 w-full">
                <p className="mt-auto text-xs mx-auto w-fit text-gray-400">
                    © 2005–2025 Graphy Inc. All rights reserved.
                </p>
            </div>
        </>
    );
};

export default Forgetpassword;