"use client"
import { useCallback, useEffect } from "react"
import RoundRadioButton from "../reuseable/RoundRadioButton"
import { SpecialProps } from "./MovementResctiction_2"
import { AnimatePresence, motion } from "framer-motion"
import { DefaultTransition } from "./ArchToTreat_1"
import { FieldError, FieldErrors, FieldValues, Path, PathValue } from "react-hook-form"


const SagitalRelationShipSide = <T extends FieldValues>({ register, errors, watch, setValue, side }: SpecialProps<T> & { side: "right" | "left" }) => {
    const improveCanine = watch(`sagitalrRelationShip.${side}.improveCanine` as Path<T>);
    const improveMolar = watch(`sagitalrRelationShip.${side}.improveMolar` as Path<T>);

    const key = `sagitalrRelationShip.${side}.option` as Path<T>
    const sagitalRelationShipRightOption = watch(key)
    const handleOptionChange = useCallback(() => {

        if (sagitalRelationShipRightOption == "doNotCorrect" || sagitalRelationShipRightOption == "Decide after 3D Simulation") {

            setValue(`sagitalrRelationShip.${side}.improveCanine` as Path<T>, "" as PathValue<T, Path<T>>)
            setValue(`sagitalrRelationShip.${side}.improveMolar` as Path<T>, "" as PathValue<T, Path<T>>)
        }
    }, [sagitalRelationShipRightOption, setValue, side]);

    const handleImproveChange = useCallback(() => {
        if (improveCanine || improveMolar) {

            setValue(`sagitalrRelationShip.${side}.option` as Path<T>, "" as PathValue<T, Path<T>>)
        }
    }, [improveCanine, improveMolar, setValue, side]);

    useEffect(() => {
        handleOptionChange();
    }, [handleOptionChange]);

    useEffect(() => {
        handleImproveChange();
    }, [handleImproveChange]);


    function getNestedError<T extends FieldValues>(
        errors: FieldErrors<T>,
        path: string[]
    ): FieldError | undefined {
        return path.reduce((acc: unknown, key: string) => {
            if (acc && typeof acc === "object" && key in acc) {
                return (acc as Record<string, unknown>)[key];
            }
            return undefined;
        }, errors as unknown) as FieldError | undefined;
    }

    // Usage:
   const optionError = getNestedError(errors, ["sagitalrRelationShip", side, "option"]);

    // const canineOptions = [
    //     {
    //         label: "IPR",
    //         name: `sagitalrRelationShip.${side}.improveCanineTreatmentOptions.ipr`,
    //     },
    //     {
    //         label: "Class II/III Elastic",
    //         name: `sagitalrRelationShip.${side}.improveCanineTreatmentOptions.class23Elastic`,
    //     },
    //     {
    //         label: "Molar Distalization",
    //         name: `sagitalrRelationShip.${side}.improveCanineTreatmentOptions.molarDistalization`,
    //     },
    //     {
    //         label: "MIA/TAD Associated Elastic",
    //         name: `sagitalrRelationShip.${side}.improveCanineTreatmentOptions.MIATADAssociatedElastic`,
    //     },
    //     {
    //         label: "Molar Mesialization",
    //         name: `sagitalrRelationShip.${side}.improveCanineTreatmentOptions.molarMesialization`,
    //     },
    // ];
    // const molarOptions = [
    //     {
    //         label: "IPR",
    //         name: `sagitalrRelationShip.${side}.improveMolarTreatmentOptions.ipr`,
    //     },
    //     {
    //         label: "Class II/III Elastic",
    //         name: `sagitalrRelationShip.${side}.improveMolarTreatmentOptions.class23Elastic`,
    //     },
    //     {
    //         label: "Molar Distalization",
    //         name: `sagitalrRelationShip.${side}.improveMolarTreatmentOptions.molarDistalization`,
    //     },
    //     {
    //         label: "MIA/TAD Associated Elastic",
    //         name: `sagitalrRelationShip.${side}.improveMolarTreatmentOptions.MIATADAssociatedElastic`,
    //     },
    //     {
    //         label: "Molar Mesialization",
    //         name: `sagitalrRelationShip.${side}.improveMolarTreatmentOptions.molarMesialization`,
    //     },
    // ];

    return (
        <div className="flex flex-col gap-2 px-3 my-4">
            <div className="flex items-center gap-2 mb-2">
                <RoundRadioButton
                    id={`${side}_Do Not Correct`}
                    label="Do Not Correct"
                    register={register}
                    name={`sagitalrRelationShip.${side}.option`}
                    value="doNotCorrect"
                    labelClass="!text-dark !text-base"
                />
                <RoundRadioButton
                    id={`${side}_Improve Canine`}
                    label="Improve Canine"
                    register={register}
                    name={`sagitalrRelationShip.${side}.improveCanine`}
                    labelClass="!text-dark !text-base"
                />
                <RoundRadioButton
                    id={`${side}_Improve Molar`}
                    label="Improve Molar"
                    register={register}
                    name={`sagitalrRelationShip.${side}.improveMolar`}
                    labelClass="!text-dark !text-base"
                />
                <RoundRadioButton
                    id={`${side}_decideLater`}
                    label="Decide after 3D Simulation"
                    register={register}
                    name={`sagitalrRelationShip.${side}.option`}
                    value="Decide after 3D Simulation"
                    labelClass="!text-dark !text-base"
                />
            </div>

            <AnimatePresence initial={false} mode="wait">
                {improveCanine && (
                    <motion.div
                        key="upper"
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={DefaultTransition}
                        style={{ overflow: 'hidden' }}
                    >
                        <div className="ps-6 flex flex-col gap-2">
                            <h3 className="text-sm text-gray my-2">Improve Canine Options</h3>
                            <div className="flex items-center gap-2">
                                <RoundRadioButton
                                    id={`${side}_Improve Only`}
                                    label="Improve only"
                                    value="Improve only"
                                    register={register}
                                    name={`sagitalrRelationShip.${side}.improveCanineOption`}
                                    labelClass="!text-dark !text-base"
                                />
                                <RoundRadioButton
                                    id={`${side}_correction to class`}
                                    label="Correction to Class I"
                                    value="Correction to Class I"
                                    register={register}
                                    name={`sagitalrRelationShip.${side}.improveCanineOption`}
                                    labelClass="!text-dark !text-base"
                                />
                            </div>
                            <textarea
                                placeholder="Note"
                                {...register(`sagitalrRelationShip.${side}.improveCanineNote` as Path<T>)}
                                className="border p-2 w-full rounded-xl"
                            />

                            {/* {canineOptions.map((option, index) => {
                                return (
                                    <div key={index} className="flex items-center justify-between">
                                        <span>{option.label}</span>
                                        <div className="flex gap-6">
                                            {["primarily", "asNeeded", "none"].map((opt, ind) => (
                                                <RoundRadioButton
                                                    key={opt}
                                                    id={`${option}-${side}-${opt}-${ind}-${index}`}
                                                    label={opt === "asNeeded" ? "As needed" : opt.charAt(0).toUpperCase() + opt.slice(1)}
                                                    value={opt}
                                                    register={register}
                                                    name={option.name}
                                                    labelClass="!text-dark text-base"
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )
                            })} */}

                        </div>
                    </motion.div>
                )}
            </AnimatePresence>



            <AnimatePresence initial={false} mode="wait">
                {improveMolar && (
                    <motion.div
                        key="upper"
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={DefaultTransition}
                        style={{ overflow: 'hidden' }}
                    >
                        <div className="ps-6 flex flex-col gap-2">
                            <h3 className="text-sm text-gray my-2">Improve Molar Options</h3>
                            <div className="flex items-center gap-2">
                                <RoundRadioButton
                                    id={`${side}_Improve Only-molar`}
                                    label="Improve only"
                                    value="Improve only"
                                    register={register}
                                    name={`sagitalrRelationShip.${side}.improveMolarOption`}
                                    labelClass="!text-dark !text-base"
                                />
                                <RoundRadioButton
                                    id={`${side}_correction to class-molar`}
                                    label="Correction to Class I"
                                    value="Correction to Class I"
                                    register={register}
                                    name={`sagitalrRelationShip.${side}.improveMolarOption`}
                                    labelClass="!text-dark !text-base"
                                />
                            </div>
                            <textarea
                                placeholder="Note"
                                {...register(`sagitalrRelationShip.${side}.improveMolarNote` as Path<T>)}
                                className="border p-2 w-full rounded-xl"
                            />

                            {/* {molarOptions.map((option, index) => {
                                return (
                                    <div key={index} className="flex items-center justify-between">
                                        <span>{option.label}</span>
                                        <div className="flex gap-6">
                                            {["primarily", "asNeeded", "none"].map((opt, ind) => (
                                                <RoundRadioButton
                                                    key={opt}
                                                    id={`${option}-${side}-${opt}-${ind}-${index}-${option.name}`}
                                                    label={opt === "asNeeded" ? "As needed" : opt.charAt(0).toUpperCase() + opt.slice(1)}
                                                    value={opt}
                                                    register={register}
                                                    name={option.name}
                                                    labelClass="!text-dark text-base"
                                                />
                                            ))}
                                        </div>
                                    </div>
                                )
                            })} */}

                        </div>
                    </motion.div>
                )}
            </AnimatePresence>



            <AnimatePresence initial={false} mode="wait">
                {sagitalRelationShipRightOption == "Decide after 3D Simulation" && (
                    <motion.div
                        key="upper"
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={DefaultTransition}
                        style={{ overflow: 'hidden' }}
                    >
                        <textarea
                            placeholder="Note"
                            {...register(`sagitalrRelationShip.${side}.note` as Path<T>)}
                            className="border p-2 w-full rounded-xl"
                        />
                    </motion.div>
                )}
            </AnimatePresence>
            {optionError?.message && (
                <p className="text-red-500 text-sm">{optionError.message}</p>
            )}
        </div>
    )
}


export default SagitalRelationShipSide