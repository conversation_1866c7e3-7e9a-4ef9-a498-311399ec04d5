import { fetchApi } from '@/app/api/getapis';
import PatientFile from '@/components/patient-file/PatientFile'
import { PatientFileData } from '@/types/types';
import { API_ROUTES } from '@/utils/ApiRoutes';
import React from 'react'

interface PageProps {
  params: Promise<{ slug?: string }>;
  searchParams: Promise<{ id?: string }>;
}

const page = async ({ searchParams }: PageProps) => {
  // Await the searchParams
  const resolvedSearchParams = await searchParams;
  const patientId = resolvedSearchParams.id;

  const patientsArray = await fetchApi(`${API_ROUTES.PATIENT.GET_PATIENT_BY_ID}/${patientId}`);
  console.log(patientsArray)
  return (
    <>
      <PatientFile data={patientsArray as PatientFileData} />
    </>
  );
};

export default page;
