"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "react-toastify";
import { Suspense } from "react";
import getAndDecryptCookie from "@/app/lib/auth";
import { updateEmployee, fetchEmployeeById } from "@/utils/ApisHelperFunction";
// Define types
type StaffRole =
  | "patient-management"
  | "prescription-management"
  | "treatment-management"
  | "payments-management"
  | "education-access"
  | "store-purchases"
  | "prospect-management";

type StaffMemberData = {
  id?: string;
  salutation: string;
  firstName: string;
  lastName: string;
  email: string;
  practicePhone: string;
  mobile: string;
  profession: string;
  role: StaffRole | null;
  status: "active" | "inactive";
};

interface StaffDetailsProps {
  mode?: "view" | "edit" | "add"; // Make mode optional with default
  staffData?: StaffMemberData;
}

// Remove the mock fetchStaffData function

const StaffDetails: React.FC<StaffDetailsProps> = ({
  mode = "view",
  staffData,
}) => {
  const router = useRouter();
  const isViewMode = mode === "view";
  const isEditMode = mode === "edit";
  const isAddMode = mode === "add";

  // Initialize state with default or provided values
  const [formData, setFormData] = useState<StaffMemberData>({
    salutation: "",
    firstName: "",
    lastName: "",
    email: "",
    practicePhone: "",
    mobile: "",
    profession: "",
    role: null,
    status: "active",
  });

  // Load staff data if provided (for view/edit modes)
  useEffect(() => {
    if (staffData && (isViewMode || isEditMode)) {
      setFormData(staffData);
    }
  }, [staffData, isViewMode, isEditMode]);

  // Form validation state
  const [errors, setErrors] = useState<
    Partial<Record<keyof StaffMemberData, string>>
  >({});
  const [submitting, setSubmitting] = useState(false);

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error when field is modified
    if (errors[name as keyof StaffMemberData]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle role selection
  const handleRoleChange = (role: StaffRole) => {
    setFormData((prev) => ({
      ...prev,
      role,
    }));

    if (errors.role) {
      setErrors((prev) => ({
        ...prev,
        role: "",
      }));
    }
  };

  // Validate form
  const validateForm = () => {
    const newErrors: Partial<Record<keyof StaffMemberData, string>> = {};

    // Required fields
    if (!formData.firstName.trim())
      newErrors.firstName = "First name is required";
    if (!formData.lastName.trim()) newErrors.lastName = "Last name is required";

    // Salutation required
    if (!formData.salutation.trim()) newErrors.salutation = "Salutation is required";

    // Profession required
    if (!formData.profession.trim()) newErrors.profession = "Profession is required";

    // Email validation
    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    // Phone validation (basic)
    if (!formData.practicePhone.trim())
      newErrors.practicePhone = "Practice phone number is required";

    // Role validation
    if (!formData.role) newErrors.role = "Please select a role";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    if (isAddMode) {
      console.log("Adding new staff member:", formData);
      // API call would go here
      toast.success("Staff member added successfully!");
      router.push("/account/staff");
    } else if (isEditMode) {
      // Integrate updateEmployee API call
      try {
        setSubmitting(true);
        const token = getAndDecryptCookie("AccessToken");
        if (!token) {
          toast.error("Authentication token missing. Please log in again.");
          setSubmitting(false);
          return;
        }
        if (!formData.id) {
          toast.error("Employee ID is missing.");
          setSubmitting(false);
          return;
        }
        // Map form fields to API fields
        const payload = {
          first_name: formData.firstName,
          last_name: formData.lastName,
          email: formData.email,
          salutation: formData.salutation,
          practice_phone_number: formData.practicePhone,
          mobile: formData.mobile,
          profession: formData.profession,
        };
        const result = await updateEmployee(formData.id, payload, token);
        if (result && result.success) {
          toast.success("Staff member updated successfully!");
          router.push("/account/staff");
        } else {
          toast.error(result?.message || "Failed to update staff member.");
        }
      } catch (error) {
        toast.error("An error occurred while updating staff member.");
      } finally {
        setSubmitting(false);
      }
    }
  };

  // Handle cancellation
  const handleCancel = () => {
    router.push("/account/staff");
  };

  return (
    <div className="w-full bg-white p-3 rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">
          {isAddMode
            ? "Invite New Staff"
            : isEditMode
            ? "Edit Staff Member"
            : "Staff Member Details"}
        </h1>
      </div>

      <div className="bg-white rounded-lg p-4">
        {isAddMode && (
          <p className="text-sm text-gray-600 mb-6">
            {`Enter staff's information below to invite`}
          </p>
        )}

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Personal Information Section */}
          <div>
            <h2 className="text-lg font-semibold text-gray-700 mb-4">Personal Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Salutation */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Salutation<span className="text-red-500">*</span></label>
                <select
                  name="salutation"
                  value={formData.salutation}
                  onChange={handleChange}
                  disabled={isViewMode}
                  className="w-full rounded-md border border-gray-300 py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"
                  title="Salutation"
                >
                  <option value="">Choose an option</option>
                  <option value="Dr.">Dr.</option>
                  <option value="Mr.">Mr.</option>
                  <option value="Mrs.">Mrs.</option>
                  <option value="Ms.">Ms.</option>
                </select>
                {errors.salutation && (
                  <p className="mt-1 text-sm text-red-500">{errors.salutation}</p>
                )}
              </div>
              {/* Profession */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Profession<span className="text-red-500">*</span></label>
                <select
                  name="profession"
                  value={formData.profession}
                  onChange={handleChange}
                  disabled={isViewMode}
                  className={`w-full rounded-md border ${errors.profession ? "border-red-500" : "border-gray-300"} py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary`}
                  title="Profession"
                >
                  <option value="">Choose an option</option>
                  <option value="Dentist">Dentist</option>
                  <option value="Orthodontist">Orthodontist</option>
                  <option value="Assistant">Assistant</option>
                  <option value="Administrator">Administrator</option>
                  <option value="Receptionist">Receptionist</option>
                </select>
                {errors.profession && (
                  <p className="mt-1 text-sm text-red-500">{errors.profession}</p>
                )}
              </div>
              {/* First Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">First Name*</label>
                <input
                  type="text"
                  name="firstName"
                  placeholder="First Name"
                  value={formData.firstName}
                  onChange={handleChange}
                  disabled={isViewMode}
                  className={`w-full rounded-md border ${errors.firstName ? "border-red-500" : "border-gray-300"} py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary`}
                />
                {errors.firstName && (
                  <p className="mt-1 text-sm text-red-500">{errors.firstName}</p>
                )}
              </div>
              {/* Last Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Last Name*</label>
                <input
                  type="text"
                  name="lastName"
                  placeholder="Last Name"
                  value={formData.lastName}
                  onChange={handleChange}
                  disabled={isViewMode}
                  className={`w-full rounded-md border ${errors.lastName ? "border-red-500" : "border-gray-300"} py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary`}
                />
                {errors.lastName && (
                  <p className="mt-1 text-sm text-red-500">{errors.lastName}</p>
                )}
              </div>
            </div>
          </div>

          {/* Contact Information Section */}
          <div>
            <h2 className="text-lg font-semibold text-gray-700 mb-4">Contact Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email*</label>
                <input
                  type="email"
                  name="email"
                  placeholder="Email Address"
                  value={formData.email}
                  onChange={handleChange}
                  disabled={isViewMode}
                  className={`w-full rounded-md border ${errors.email ? "border-red-500" : "border-gray-300"} py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary`}
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-red-500">{errors.email}</p>
                )}
              </div>
              {/* Practice Phone */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Practice Phone Number*</label>
                <input
                  type="tel"
                  name="practicePhone"
                  placeholder="07254671903"
                  value={formData.practicePhone}
                  onChange={handleChange}
                  disabled={isViewMode}
                  className={`w-full rounded-md border ${errors.practicePhone ? "border-red-500" : "border-gray-300"} py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary`}
                />
                {errors.practicePhone && (
                  <p className="mt-1 text-sm text-red-500">{errors.practicePhone}</p>
                )}
              </div>
              {/* Mobile */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Mobile</label>
                <input
                  type="tel"
                  name="mobile"
                  placeholder="07254671903"
                  value={formData.mobile}
                  onChange={handleChange}
                  disabled={isViewMode}
                  className="w-full rounded-md border border-gray-300 py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"
                />
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-between pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={handleCancel}
              className="px-6 py-2 border border-gray-300 rounded-full text-gray-700 font-medium"
            >
              {isViewMode ? "Back" : "Cancel"}
            </button>

            {!isViewMode && (
              <button
                type="submit"
                className="px-6 py-2 bg-primary text-white rounded-full font-medium"
                disabled={submitting}
              >
                {isAddMode ? "Invite" : submitting ? "Saving..." : "Save Changes"}
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  );
};

const StaffDetailsContent = () => {
  const searchParams = useSearchParams();
  const mode = (searchParams.get("mode") as "view" | "edit" | "add") || "view";
  const id = searchParams.get("id");

  const [staffData, setStaffData] = useState<StaffMemberData | undefined>(
    undefined
  );
  const [loading, setLoading] = useState(mode !== "add");
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if ((mode === "view" || mode === "edit") && id) {
      setLoading(true);
      setError(null);
      const token = getAndDecryptCookie("AccessToken");
      if (!token) {
        setError("Authentication token missing. Please log in again.");
        setLoading(false);
        return;
      }
      fetchEmployeeById(id, token)
        .then((data) => {
          if (data) {
            // Map API fields to form fields
            setStaffData({
              id: data.id?.toString(),
              salutation: data.salutation || "",
              firstName: data.first_name || "",
              lastName: data.last_name || "",
              email: data.email || "",
              practicePhone: data.practice_phone_number || "",
              mobile: data.mobile || "",
              profession: data.profession || "",
              role: (data.role as StaffRole) || null,
              status: "active", // API does not provide status, default to active
            });
          } else {
            setError("Failed to fetch staff data.");
          }
          setLoading(false);
        })
        .catch((error) => {
          setError("Error fetching staff data.");
          setLoading(false);
        });
    } else {
      setLoading(false);
    }
  }, [mode, id]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-red-500 text-lg">{error}</div>
      </div>
    );
  }

  return <StaffDetails mode={mode} staffData={staffData} />;
};
const StaffDetailsPage = () => {
  return (
    <Suspense
      fallback={
        <div className="flex justify-center items-center min-h-screen">
          Loading staff details...
        </div>
      }
    >
      <StaffDetailsContent />
    </Suspense>
  );
};

export default StaffDetailsPage;
