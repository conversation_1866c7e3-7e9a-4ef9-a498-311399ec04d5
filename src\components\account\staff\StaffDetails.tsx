'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'react-toastify';
import { Suspense } from 'react';
// Define types
type StaffRole =
    | 'patient-management'
    | 'prescription-management'
    | 'treatment-management'
    | 'payments-management'
    | 'education-access'
    | 'store-purchases'
    | 'prospect-management';

type StaffMemberData = {
    id?: string;
    salutation: string;
    firstName: string;
    lastName: string;
    email: string;
    practicePhone: string;
    mobile: string;
    profession: string;
    role: StaffRole | null;
    status: 'active' | 'inactive';
};

interface StaffDetailsProps {
    mode?: 'view' | 'edit' | 'add'; // Make mode optional with default
    staffData?: StaffMemberData;
}



// Mock function to fetch staff data - replace with your actual API call
const fetchStaffData = async (id: string) => {
    // In a real app, this would be an API call
    return {
        id,
        salutation: 'Dr.',
        firstName: 'John',
        lastName: 'Doe',
        email: `<EMAIL>`,
        practicePhone: '07254671903',
        mobile: '07254671904',
        profession: 'Orthodontist',
        role: 'treatment-management' as const,
        status: 'active' as const
    };
};



const StaffDetails: React.FC<StaffDetailsProps> = ({ mode = 'view', staffData }) => {
    const router = useRouter();
    const isViewMode = mode === 'view';
    const isEditMode = mode === 'edit';
    const isAddMode = mode === 'add';

    // Initialize state with default or provided values
    const [formData, setFormData] = useState<StaffMemberData>({
        salutation: '',
        firstName: '',
        lastName: '',
        email: '',
        practicePhone: '',
        mobile: '',
        profession: '',
        role: null,
        status: 'active'
    });

    // Load staff data if provided (for view/edit modes)
    useEffect(() => {
        if (staffData && (isViewMode || isEditMode)) {
            setFormData(staffData);
        }
    }, [staffData, isViewMode, isEditMode]);

    // Form validation state
    const [errors, setErrors] = useState<Partial<Record<keyof StaffMemberData, string>>>({});

    // Handle input changes
    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));

        // Clear error when field is modified
        if (errors[name as keyof StaffMemberData]) {
            setErrors(prev => ({
                ...prev,
                [name]: ''
            }));
        }
    };

    // Handle role selection
    const handleRoleChange = (role: StaffRole) => {
        setFormData(prev => ({
            ...prev,
            role
        }));

        if (errors.role) {
            setErrors(prev => ({
                ...prev,
                role: ''
            }));
        }
    };

    // Validate form
    const validateForm = () => {
        const newErrors: Partial<Record<keyof StaffMemberData, string>> = {};

        // Required fields
        if (!formData.firstName.trim()) newErrors.firstName = 'First name is required';
        if (!formData.lastName.trim()) newErrors.lastName = 'Last name is required';

        // Email validation
        if (!formData.email.trim()) {
            newErrors.email = 'Email is required';
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = 'Please enter a valid email address';
        }

        // Phone validation (basic)
        if (!formData.practicePhone.trim()) newErrors.practicePhone = 'Practice phone number is required';

        // Role validation
        if (!formData.role) newErrors.role = 'Please select a role';

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Handle form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (!validateForm()) return;

        if (isAddMode) {
            console.log('Adding new staff member:', formData);
            // API call would go here
            toast.success('Staff member added successfully!');
            router.push('/account/staff');
        } else if (isEditMode) {
            console.log('Updating staff member:', formData);
            // API call would go here
            toast.success('Staff member updated successfully!');
            router.push('/account/staff');
        }
    };

    // Handle cancellation
    const handleCancel = () => {
        router.push('/account/staff');
    };

    return (
        <div className="w-full bg-white p-3 rounded-lg shadow-md">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-2xl font-bold text-gray-800">
                    {isAddMode ? 'Invite New Staff' : isEditMode ? 'Edit Staff Member' : 'Staff Member Details'}
                </h1>
            </div>

            <div className="bg-white rounded-lg p-4">
                {isAddMode && (
                    <p className="text-sm text-gray-600 mb-6">
                        {`Enter staff's information below to invite`}
                    </p>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Left Column */}
                        <div className="space-y-6">
                            {/* Salutation */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Salutation
                                </label>
                                <select
                                    name="salutation"
                                    value={formData.salutation}
                                    onChange={handleChange}
                                    disabled={isViewMode}
                                    className="w-full rounded-md border border-gray-300 py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"
                                >
                                    <option value="">Choose an option</option>
                                    <option value="Dr.">Dr.</option>
                                    <option value="Mr.">Mr.</option>
                                    <option value="Mrs.">Mrs.</option>
                                    <option value="Ms.">Ms.</option>
                                </select>
                            </div>

                            {/* Staff Name */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Staff Member Name*
                                </label>
                                <div className="space-y-3">
                                    <div>
                                        <input
                                            type="text"
                                            name="lastName"
                                            placeholder="Last Name"
                                            value={formData.lastName}
                                            onChange={handleChange}
                                            disabled={isViewMode}
                                            className={`w-full rounded-md border ${errors.lastName ? 'border-red-500' : 'border-gray-300'} py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary`}
                                        />
                                        {errors.lastName && (
                                            <p className="mt-1 text-sm text-red-500">{errors.lastName}</p>
                                        )}
                                    </div>
                                    <div>
                                        <input
                                            type="text"
                                            name="firstName"
                                            placeholder="First Name"
                                            value={formData.firstName}
                                            onChange={handleChange}
                                            disabled={isViewMode}
                                            className={`w-full rounded-md border ${errors.firstName ? 'border-red-500' : 'border-gray-300'} py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary`}
                                        />
                                        {errors.firstName && (
                                            <p className="mt-1 text-sm text-red-500">{errors.firstName}</p>
                                        )}
                                    </div>
                                </div>
                            </div>

                            {/* Email */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Email*
                                </label>
                                <input
                                    type="email"
                                    name="email"
                                    placeholder="Email Address"
                                    value={formData.email}
                                    onChange={handleChange}
                                    disabled={isViewMode}
                                    className={`w-full rounded-md border ${errors.email ? 'border-red-500' : 'border-gray-300'} py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary`}
                                />
                                {errors.email && (
                                    <p className="mt-1 text-sm text-red-500">{errors.email}</p>
                                )}
                            </div>

                            {/* Practice Phone */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Practice Phone Number*
                                </label>
                                <input
                                    type="tel"
                                    name="practicePhone"
                                    placeholder="07254671903"
                                    value={formData.practicePhone}
                                    onChange={handleChange}
                                    disabled={isViewMode}
                                    className={`w-full rounded-md border ${errors.practicePhone ? 'border-red-500' : 'border-gray-300'} py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary`}
                                />
                                {errors.practicePhone && (
                                    <p className="mt-1 text-sm text-red-500">{errors.practicePhone}</p>
                                )}
                            </div>

                            {/* Mobile */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Mobile
                                </label>
                                <input
                                    type="tel"
                                    name="mobile"
                                    placeholder="07254671903"
                                    value={formData.mobile}
                                    onChange={handleChange}
                                    disabled={isViewMode}
                                    className="w-full rounded-md border border-gray-300 py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary"
                                />
                            </div>

                            {/* Profession */}
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Profession*
                                </label>
                                <select
                                    name="profession"
                                    value={formData.profession}
                                    onChange={handleChange}
                                    disabled={isViewMode}
                                    className={`w-full rounded-md border ${errors.profession ? 'border-red-500' : 'border-gray-300'} py-2 px-3 focus:outline-none focus:ring-primary focus:border-primary`}
                                >
                                    <option value="">Choose an option</option>
                                    <option value="Dentist">Dentist</option>
                                    <option value="Orthodontist">Orthodontist</option>
                                    <option value="Assistant">Assistant</option>
                                    <option value="Administrator">Administrator</option>
                                    <option value="Receptionist">Receptionist</option>
                                </select>
                                {errors.profession && (
                                    <p className="mt-1 text-sm text-red-500">{errors.profession}</p>
                                )}
                            </div>
                        </div>

                        {/* Right Column - Role Selection */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-3">
                                {`Select staff member's role`}
                            </label>

                            <div className="space-y-3">
                                {/* Patient management */}
                                <div className="flex items-start">
                                    <div className="flex h-5 items-center">
                                        <input
                                            id="patient-management"
                                            type="radio"
                                            disabled={isViewMode}
                                            checked={formData.role === 'patient-management'}
                                            onChange={() => handleRoleChange('patient-management')}
                                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300"
                                        />
                                    </div>
                                    <div className="ml-3 text-sm">
                                        <label htmlFor="patient-management" className="font-medium text-gray-700">Patient management</label>
                                        <p className="text-gray-500">Add patient, Upload photos</p>
                                    </div>
                                </div>

                                {/* Prescription management */}
                                <div className="flex items-start">
                                    <div className="flex h-5 items-center">
                                        <input
                                            id="prescription-management"
                                            type="radio"
                                            disabled={isViewMode}
                                            checked={formData.role === 'prescription-management'}
                                            onChange={() => handleRoleChange('prescription-management')}
                                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300"
                                        />
                                    </div>
                                    <div className="ml-3 text-sm">
                                        <label htmlFor="prescription-management" className="font-medium text-gray-700">Prescription management</label>
                                        <p className="text-gray-500">Submit prescription forms, review CheckCheck treatment plans (roles require approval)</p>
                                    </div>
                                </div>

                                {/* Treatment management */}
                                <div className="flex items-start">
                                    <div className="flex h-5 items-center">
                                        <input
                                            id="treatment-management"
                                            type="radio"
                                            disabled={isViewMode}
                                            checked={formData.role === 'treatment-management'}
                                            onChange={() => handleRoleChange('treatment-management')}
                                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300"
                                        />
                                    </div>
                                    <div className="ml-3 text-sm">
                                        <label htmlFor="treatment-management" className="font-medium text-gray-700">Treatment management</label>
                                        <p className="text-gray-500">Review & approve CheckCheck treatment plans, Submit prescription forms</p>
                                    </div>
                                </div>

                                {/* Payments management */}
                                <div className="flex items-start">
                                    <div className="flex h-5 items-center">
                                        <input
                                            id="payments-management"
                                            type="radio"
                                            disabled={isViewMode}
                                            checked={formData.role === 'payments-management'}
                                            onChange={() => handleRoleChange('payments-management')}
                                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300"
                                        />
                                    </div>
                                    <div className="ml-3 text-sm">
                                        <label htmlFor="payments-management" className="font-medium text-gray-700">Payments management</label>
                                        <p className="text-gray-500">Make payments, View account summary</p>
                                    </div>
                                </div>

                                {/* Education access */}
                                <div className="flex items-start">
                                    <div className="flex h-5 items-center">
                                        <input
                                            id="education-access"
                                            type="radio"
                                            disabled={isViewMode}
                                            checked={formData.role === 'education-access'}
                                            onChange={() => handleRoleChange('education-access')}
                                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300"
                                        />
                                    </div>
                                    <div className="ml-3 text-sm">
                                        <label htmlFor="education-access" className="font-medium text-gray-700">Education access</label>
                                    </div>
                                </div>

                                {/* Store purchases */}
                                <div className="flex items-start">
                                    <div className="flex h-5 items-center">
                                        <input
                                            id="store-purchases"
                                            type="radio"
                                            disabled={isViewMode}
                                            checked={formData.role === 'store-purchases'}
                                            onChange={() => handleRoleChange('store-purchases')}
                                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300"
                                        />
                                    </div>
                                    <div className="ml-3 text-sm">
                                        <label htmlFor="store-purchases" className="font-medium text-gray-700">Store purchases</label>
                                        <p className="text-gray-500">Submit Align iStore orders</p>
                                    </div>
                                </div>

                                {/* Prospect management */}
                                <div className="flex items-start">
                                    <div className="flex h-5 items-center">
                                        <input
                                            id="prospect-management"
                                            type="radio"
                                            disabled={isViewMode}
                                            checked={formData.role === 'prospect-management'}
                                            onChange={() => handleRoleChange('prospect-management')}
                                            className="h-4 w-4 text-primary focus:ring-primary border-gray-300"
                                        />
                                    </div>
                                    <div className="ml-3 text-sm">
                                        <label htmlFor="prospect-management" className="font-medium text-gray-700">Prospect management</label>
                                        <p className="text-gray-500">Access SmileView and Doctor Locator prospective patients</p>
                                    </div>
                                </div>

                                {errors.role && (
                                    <p className="mt-1 text-sm text-red-500">{errors.role}</p>
                                )}
                            </div>

                            {/* Note about roles */}
                            <div className="mt-4 text-xs text-gray-500">
                                <p>Note: All staff roles include access to the Support pages.</p>
                                <p>The staff role can always be edited in Staff and User pages.</p>
                            </div>

                            {/* Legal disclaimer */}
                            <div className="mt-6 text-xs text-gray-500">
                                <p>{`* Doctor acknowledges that the Invisalign System and certain other technologies and know-how products may be protected by intellectual property rights of Align Technology, Inc. or its affiliated entities ("Align"). Checking the box and approval of a staff member to access, perform or use the authorized tasks and tools is a certification made to Align that the staff member is employed by the practice, bound by the appropriate confidentiality restrictions, and authorized to perform the tasks specified in a staff member's role.`}</p>
                            </div>
                        </div>
                    </div>

                    {/* Form Actions */}
                    <div className="flex justify-between pt-6 border-t border-gray-200">
                        <button
                            type="button"
                            onClick={handleCancel}
                            className="px-6 py-2 border border-gray-300 rounded-full text-gray-700 font-medium"
                        >
                            {isViewMode ? 'Back' : 'Cancel'}
                        </button>

                        {isViewMode && (
                            <button
                                type="button"
                                onClick={() => router.push(`/account/staff/staff-details?mode=edit&id=${staffData?.id}`)}
                                className="px-4 py-2 bg-primary text-white rounded-full cursor-pointer font-medium flex items-center gap-2"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path d="M17.3287 7.26797L18.5487 6.04797C19.8157 4.78097 19.8157 2.71897 18.5487 1.45147C17.9352 0.838465 17.1197 0.501465 16.2502 0.501465C15.3807 0.501465 14.5647 0.838965 13.9517 1.45196L12.7322 2.67147L17.3287 7.26797ZM11.6717 3.73197L2.63723 12.7665C2.44473 12.959 2.29823 13.1965 2.21323 13.454L0.538232 18.5145C0.448732 18.7835 0.519232 19.08 0.719732 19.2805C0.863232 19.4235 1.05423 19.5 1.25023 19.5C1.32923 19.5 1.40873 19.4875 1.48623 19.462L6.54523 17.7865C6.80373 17.7015 7.04173 17.555 7.23423 17.362L16.2682 8.32797L11.6717 3.73197Z" fill="#FFF" />
                                </svg> Edit
                            </button>
                        )}
                        {!isViewMode && (
                            <button
                                type="submit"
                                className="px-6 py-2 bg-primary text-white rounded-full font-medium"
                            >
                                {isAddMode ? 'Invite' : 'Save Changes'}
                            </button>
                        )}
                    </div>
                </form>
            </div>
        </div>
    );
};




const StaffDetailsContent = () => {
    const searchParams = useSearchParams();
    const mode = searchParams.get('mode') as 'view' | 'edit' | 'add' || 'view';
    const id = searchParams.get('id');

    const [staffData, setStaffData] = useState<StaffMemberData | undefined>(undefined);
    const [loading, setLoading] = useState(mode !== 'add');

    useEffect(() => {
        if ((mode === 'view' || mode === 'edit') && id) {
            setLoading(true);
            fetchStaffData(id)
                .then(data => {
                    setStaffData(data);
                    setLoading(false);
                })
                .catch(error => {
                    console.error('Error fetching staff data:', error);
                    setLoading(false);
                });
        } else {
            setLoading(false);
        }
    }, [mode, id]);

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
            </div>
        );
    }

    return <StaffDetails mode={mode} staffData={staffData} />;
};
const StaffDetailsPage = () => {


    return (

        <Suspense fallback={<div className="flex justify-center items-center min-h-screen">Loading staff details...</div>}>
            <StaffDetailsContent />
        </Suspense>
    )

};

export default StaffDetailsPage;
