import React from 'react';
import { UseFormRegisterReturn } from 'react-hook-form';

interface CustomTextareaProps {
    placeholder?: string;
    error?: string;
    register: UseFormRegisterReturn;
    className?: string;
    containerClassName?: string;
    rows?: number;
}

const CustomTextarea: React.FC<CustomTextareaProps> = ({
    placeholder,
    error,
    register,
    className = '',
    containerClassName = '',
    rows = 4
}) => {
    return (
        <div className={`${containerClassName}`}>
            <textarea
                rows={rows}
                placeholder={placeholder}
                {...register}
                className={`w-full px-4 py-2 border resize-none ${error ? 'border-red-500' : 'border-gray/40'
                    } rounded-[30px] outline-none ${className}`}
            />
            {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
        </div>
    );
};

export default CustomTextarea;
