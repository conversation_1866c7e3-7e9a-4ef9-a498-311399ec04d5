import { fetchApi } from '@/app/api/getapis';
import TreatmentOptions from '@/components/treatment-options/TreatmentOptions';
import { PlansResponse } from '@/types/types';
import { API_ROUTES } from '@/utils/ApiRoutes';
import React from 'react';

export const dynamic = 'force-dynamic';

const Page = async () => {
  const response = await fetchApi(`${API_ROUTES.PLAN.GET_PLANS}`) as PlansResponse;
  return <TreatmentOptions data={response?.plans} />;
};

export default Page;
