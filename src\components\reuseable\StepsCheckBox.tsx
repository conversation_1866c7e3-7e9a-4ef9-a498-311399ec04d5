import React, { useEffect, useRef, useState } from 'react';
import { UseFormRegisterReturn } from 'react-hook-form';
import TickIcon from './Icons/TickIcon';

interface CustomCheckboxProps {
    label: string;
    sup?: string;
    labelClass?: string
    register?: UseFormRegisterReturn
    error?: string;
    className?: string;
    id: string;
    value?: string;
    onChange: (e: string) => void;
    name?: string
    rootLableClassName?: string
    defaulChecked?: boolean
    disabled?: boolean
    disabledClass?: string
    fadeOnDisable?: boolean
}

const StepsCheckBox: React.FC<CustomCheckboxProps> = ({fadeOnDisable, disabledClass, label, register, error, className, id, value, labelClass, sup, rootLableClassName, onChange, defaulChecked, disabled = false }) => {
    const inputRef = useRef<HTMLInputElement | null>(null);
    const [render, setRender] = useState<boolean>(false)

    const reRender = () => {
        onChange(value || "")
        setRender((prev) => !prev)
    }

    useEffect(() => {
        if(disabled){
            if(inputRef.current?.checked){
                inputRef.current.checked = false
            }
        }
    }, [disabled])


    return (
        <div className={`flex items-center gap-1 ${(fadeOnDisable && disabled) && "opacity-40"}`}>
            {false && render && <span></span>}
            <label htmlFor={id} className={` flex items-center gap-1.5 cursor-pointer ${rootLableClassName} flex-col-reverse gap-1`}>
                <input
                    disabled={disabled}
                    onClick={reRender}
                    value={value}
                    type="checkbox"
                    id={id}
                    {...register}
                    className="peer hidden"
                    defaultChecked={defaulChecked}
                    ref={(el) => {
                        register?.ref?.(el);
                        inputRef.current = el;
                    }}
                />
                <div className={`w-6 h-6 appearance-none flex justify-center items-center rounded-sm  bg-gray/20 peer-checked:bg-primary ${disabled && disabledClass} peer-checked:border-transparent ${className}`}>
                    {(inputRef.current?.checked && !disabled) && <TickIcon fill='#FFFFFF' classess='w-3 h-3' />}
                </div>
                <span className={`${labelClass} text-dark ${disabled && "font-bold"} ${(inputRef.current?.checked && !disabled) && "text-primary"}`}>{label}<sup>{sup}</sup></span>
            </label>

            {error && <p className="text-red-500 text-sm">{error}</p>}
        </div>
    );
};

export default StepsCheckBox;
