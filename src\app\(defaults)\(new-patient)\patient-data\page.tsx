import { API_ROUTES } from '@/utils/ApiRoutes';
import MainPatientData from '../../../../components/patient-data/MainPatientData';
import React from 'react';
import { fetchApi } from '@/app/api/getapis';
import { Address } from '@/types/types';

export const dynamic = 'force-dynamic';

const page = async () => {
  const data = await fetchApi(`${API_ROUTES.ADRESSES.GET_ADRESS}`) as Address[];
  console.log("🚀 ~ page ~ data:", data)
  return <MainPatientData data={data} />;
};

export default page;
