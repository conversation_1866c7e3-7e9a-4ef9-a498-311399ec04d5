"use client";
import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import FormWrapper from '../reuseable/FormWrapper';
import RoundRadioButton from '../reuseable/RoundRadioButton';
import Image from 'next/image';
import closeIcon from "../../../public/svgs/icons8_close 1.svg"
import xRayLeft from "../../../public/svgs/bg-xray-left 1.svg"
import xRayRight from "../../../public/svgs/bg-xray-right 1.svg"
import addIcon from "../../../public/svgs/icons8_add 1.svg"
import UploadedRecords from '../reuseable/UploadedRecords';
import DisableOverlay from '../reuseable/DisableOverlay';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';




const patientDataSchema = z.object({
  uploadOption: z.string().min(1, "First name is required").optional()})

type UserFormData = z.infer<typeof patientDataSchema>;

const Radiographs = () => {
  const router = useRouter();
  const { register, handleSubmit } = useForm<UserFormData>({
    resolver: zodResolver(patientDataSchema),
  });
  const [uploadOption, setUploadOption] = useState<string | undefined>('now');
  const [radioGraph1, setRadioGraph1] = useState<File | null>(null);
  const [radioGraph2, setRadioGraph2] = useState<File | null>(null);

  const onSubmit = (data: UserFormData) => {
    console.log("Valid form data:", { uploadOption: data.uploadOption, radioGraph1, radioGraph2 });
    localStorage.setItem("radiograph", JSON.stringify({ uploadOption: data.uploadOption, radioGraph1, radioGraph2 }))

    setUploadOption(data.uploadOption)
    router.push("/patient-file");
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, set: (files: File) => void) => {
    const files = Array.from(e.target.files || []);
    set(files[0]);
  };


  const handleRemoveFile = (fileName: string) => {
    if(fileName === "1") {
      setRadioGraph1(null)
    }else {
      setRadioGraph2(null)
    }
  };

  useEffect(() => {
    console.log(1, radioGraph1)
    console.log(2, radioGraph2)
  }, [radioGraph1, radioGraph2])

  return (
    <>
      <FormWrapper classNames='!grid-cols-1' onSubmit={handleSubmit(onSubmit)} onBack={() => router.back()}>
        <div className='col-span-1 flex flex-col flex-grow'>
          <div className='flex xl:gap-20 gap-4 flex-wrap mb-4'>
            <RoundRadioButton
              onClick={(e: React.MouseEvent<HTMLInputElement>) => setUploadOption((e.target as HTMLInputElement).value)}
              id="image-now"
              label="Upload radiographs now"
              value="now"
              name="uploadOption"
              register={register}
              defaultChecked={uploadOption === 'now'}
              labelClass='!text-dark font-medium'

            />
            <RoundRadioButton
              onClick={(e: React.MouseEvent<HTMLInputElement>) => setUploadOption((e.target as HTMLInputElement).value)}
              id="image-later"
              label="Upload radiographs later"
              value="later"
              name="uploadOption"
              register={register}
              defaultChecked={uploadOption === 'later'}
              labelClass='!text-dark font-medium'

            />

            <RoundRadioButton
              onClick={(e: React.MouseEvent<HTMLInputElement>) => setUploadOption((e.target as HTMLInputElement).value)}
              id="image-none"
              label="No radiographs"
              value="none"
              name="uploadOption"
              register={register}
              defaultChecked={uploadOption === 'none'}
              labelClass='!text-dark font-medium'

            />
          </div>

          <div className='grid !grid-cols-12 !gap-6 flex-grow'>
            <div className="relative xl:col-span-8 col-span-12 2xl:max-h-[400px] grid 2xl:grid-cols-2 grid-cols-1 gap-4 p-8 !bg-primaryLight rounded-[10px]">
            <DisableOverlay active={uploadOption == "later" || uploadOption == "none"} color={"bg-black/40"} />
              <div className='bg-white relative rounded-[10px] p-8 col-span-1 flex flex-col items-center justify-center'>
                
                <div>
                  <Image src={xRayLeft} alt='Cross icon' />
                  <div>
                    {uploadOption === "now" && (
                      <div className='flex justify-end w-full absolute bottom-[6%] right-[5%]'>
                        <label htmlFor="file-upload-1" className='cursor-pointer'>
                          <Image src={addIcon} alt='Upload icon' className='w-8 h-8' />
                        </label>
                        <input
                          id="file-upload-1"
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={(e) => handleFileChange(e, setRadioGraph1)}
                          className="hidden"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className='bg-white relative rounded-[10px] p-8 col-span-1 flex flex-col items-center justify-center'>
                <div>
                  <Image src={xRayRight} alt='Cross icon' />
                  <div>
                    {uploadOption === "now" && (
                      <div className='flex justify-end w-full absolute bottom-[6%] right-[5%]'>
                        <label htmlFor="file-upload-2" className='cursor-pointer'>
                          <Image src={addIcon} alt='Upload icon' className='w-8 h-8' />
                        </label>
                        <input
                          id="file-upload-2"
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={(e) => handleFileChange(e, setRadioGraph2)}
                          className="hidden"
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>

            </div>


            <div className="xl:col-span-4 col-span-12 flex flex-col gap-4">
              <UploadedRecords withScan={false} disabled={uploadOption == "later" || uploadOption == "none"}/>
              <div className='grid grid-cols-2 gap-2'>
                {radioGraph1 && (
                  <div className='relative'>
                    <Image
                      src={URL.createObjectURL(radioGraph1)}
                      alt={`Uploaded portrait`}
                      width={150}
                      height={150}
                      className="rounded-[10px] object-cover w-full h-[200px]"
                    />
                    <button
                      type="button"
                      className="absolute top-0 right-0 bg-danger/30 text-white rounded-full p-1 w-7 h-7 flex items-center justify-center"
                      onClick={() => handleRemoveFile("1")}
                    >
                      <Image src={closeIcon} alt='Cross icon' className='w-5 h-5 cursor-pointer' />

                    </button>
                  </div>


                )}
                {radioGraph2 && (
                  <div className='relative'>
                    <Image
                      src={URL.createObjectURL(radioGraph2)}
                      alt={`Uploaded portrait`}
                      width={150}
                      height={150}
                      className="rounded-[10px] object-cover w-full h-[200px]"
                    />
                    <button
                      type="button"
                      className="absolute top-0 right-0 bg-danger/30 text-white rounded-full p-1 w-7 h-7 flex items-center justify-center"
                      onClick={() => handleRemoveFile("2")}
                    >
                      <Image src={closeIcon} alt='Cross icon' className='w-5 h-5 cursor-pointer' />

                    </button>
                  </div>


                )}
              </div>
            </div>



          </div>
        </div>

      </FormWrapper>
    </>
  );
};

export default Radiographs;
