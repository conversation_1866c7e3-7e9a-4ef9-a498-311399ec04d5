'use client'

import { createContext, useContext, useEffect, useState } from 'react';
import { fetchApi } from '@/app/api/getapis';
import { useRouter, usePathname } from 'next/navigation';
import { getToken, removeToken, clearAllCookies } from '@/app/lib/auth';
import { API_ROUTES } from '@/utils/ApiRoutes';

// User data interface
interface UserData {
    id: number;
    email: string;
    username: string;
    first_name: string;
    last_name: string;
    role_id: number;
    is_verified: boolean;
    profile_image: string | null;
}

// Session API response interface
interface SessionResponse {
    status: number;
    success: boolean;
    data: UserData;
    message: string;
}

// Update session context type
interface SessionContextType {
    isAuthenticated: boolean;
    user: UserData | null;
    loading: boolean;
    checkUserSession: () => Promise<void>;
}

const SessionContext = createContext<SessionContextType | undefined>(undefined);

export function SessionProvider({ children }: { children: React.ReactNode }) {
    const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [user, setUser] = useState<UserData | null>(null);
    const [loading, setLoading] = useState(true);
    const [hasInitialized, setHasInitialized] = useState(false);
    const router = useRouter();
    const pathname = usePathname();

    // Define public routes
    const publicRoutes = ['/login', '/forgot-password'];
    const isPublicRoute = publicRoutes.includes(pathname);

    const checkUserSession = async () => {
        try {
            setLoading(true);

            // First check if token exists in cookies using your function
            const token = getToken('AccessToken');

            if (!token) {
                // No token found, user is not authenticated
                setIsAuthenticated(false);
                setUser(null);

                // Only redirect to login if on protected route
                if (!isPublicRoute) {
                    router.push('/login');
                }
                return;
            }

            // Token exists, verify with server
            const data = await fetchApi(`${API_ROUTES.AUTH.CHECK_SESSION}`) as SessionResponse;
            console.log("🚀 ~ checkUserSession ~ result:", data);

            if (data) {
                setIsAuthenticated(true);
                setUser(data.data || null);

                // If user is on login page and authenticated, redirect to dashboard
                if (pathname === '/login') {
                    router.push('/dashboard');
                }
            } else {
                setIsAuthenticated(false);
                setUser(null);
                // Use your cookie functions to clear cookies properly
                removeToken('AccessToken');
                removeToken('Role');
                removeToken('Email');
                // Or use your clearAllCookies function
                // clearAllCookies();

                // Only redirect to login if on protected route
                if (!isPublicRoute) {
                    router.push('/login');
                }
            }
        } catch (error) {
            console.error('Session check failed:', error);
            setIsAuthenticated(false);
            setUser(null);

            // Clear tokens on error as well
            if (typeof window !== 'undefined') {
                localStorage.clear();
            }
            clearAllCookies();
        } finally {
            setLoading(false);
            setHasInitialized(true);
        }
    };

    useEffect(() => {
        checkUserSession();
        // Only run session check when pathname changes or on initial load
        // if (!hasInitialized) {
        //     checkUserSession();
        // } else {
        //     // If already initialized, just check if we need to redirect based on current auth state
        //     if (pathname === '/login' && isAuthenticated) {
        //         router.push('/dashboard');
        //     } else if (!isPublicRoute && !isAuthenticated) {
        //         router.push('/login');
        //     }
        // }
    }, [pathname]);

    return (
        <SessionContext.Provider value={{
            isAuthenticated,
            user,
            loading,
            checkUserSession: () => {
                setHasInitialized(false);
                return checkUserSession();
            }
        }}>
            {loading && !hasInitialized ? (
                <div className="flex items-center justify-center min-h-screen">
                    <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
                </div>
            ) : (
                children
            )}
        </SessionContext.Provider>
    );
}

export const useSession = () => {
    const context = useContext(SessionContext);
    if (context === undefined) {
        throw new Error('useSession must be used within a SessionProvider');
    }
    return context;
};