import React from 'react';
import BackButton from './BackButton';
import CancelButton from './CancelButton';
import PrimaryButton from './PrimaryButton';

interface FormWrapperProps {
  children: React.ReactNode;
  onSubmit: () => void;
  onBack?: () => void;
  showBackButton?: boolean;
  showCancelButton?: boolean;
  showNextButton?: boolean;
  classNames?: string;
}

const FormWrapper: React.FC<FormWrapperProps> = ({
  children,
  onSubmit,
  onBack,
  showBackButton = true,
  showCancelButton = false,
  showNextButton = true,
  classNames
}) => {
  return (
    <div className="flex-grow flex flex-col">
      <form
        className={`${classNames} grid xl:grid-cols-2 grid-cols-1 2xl:gap-44 xl:gap-16 gap-6 p-8 bg-white custom-shadow rounded-2xl 2xl:my-10 mt-2 mb-5 flex-grow`}
      >
        {children}
      </form>

      <div className="flex justify-between">
        <div className="flex gap-2">
          {showBackButton && <div><BackButton onClick={onBack} /></div>}
          {showCancelButton && <div><CancelButton onClick={onBack} /></div>}

        </div>
        {showNextButton && <PrimaryButton onClick={onSubmit} text="Next" />}
      </div>
    </div>
  );
};

export default FormWrapper;
