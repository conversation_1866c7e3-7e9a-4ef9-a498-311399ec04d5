import { CreatePatientResponse } from "@/types/types";
import { API_ROUTES } from "./ApiRoutes";

// Define proper response types
interface ApiResponse {
  status: number;
  success: boolean;
  message: string;
  data?: Record<string, unknown>;
}

interface LoginResponse extends ApiResponse {
  data?: {
    accessToken?: string;
    user?: Record<string, unknown>;
  };
}

interface ForgetPasswordResponse extends ApiResponse {
  data?: {
    message?: string;
  };
}

interface ClinicalConditionsResponse extends ApiResponse {
  data?: {
    conditions?: Record<string, unknown>;
  };
}

interface PatientDataResponse extends ApiResponse {
  data?: {
    records?: Record<string, unknown>;
  };
}

interface CasePrescriptionResponse extends ApiResponse {
  data?: {
    prescription?: Record<string, unknown>;
  };
}

export const loginUser = async (
  identifier: string, 
  password: string, 
  remember?: boolean
): Promise<LoginResponse | null> => {
  try {
    const formData = new FormData();
    const isEmail = identifier.includes('@') && identifier.includes('.com');

    // Add appropriate field based on identifier type
    if (isEmail) {
      formData.append('email', identifier);
    } else {
      formData.append('username', identifier);
    }

    formData.append('password', password);
    if (remember) {
      formData.append('remember', remember.toString());
    }

    const response = await fetch(`${API_ROUTES.AUTH.LOGIN}`, {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      console.error('Login failed:', response.statusText);
      return null;
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Login error:', error);
    return null;
  }
};

export const forgetPassword = async (identifier: string): Promise<ForgetPasswordResponse | null> => {
  try {
    const formData = new FormData();
    const isEmail = identifier.includes('@') && identifier.includes('.');

    if (isEmail) {
      formData.append('email', identifier);
    } else {
      formData.append('username', identifier);
    }

    const response = await fetch(`${API_ROUTES.AUTH.FORGET_PASSWORD}`, {
      method: 'POST',
      headers: {
        'accept': 'application/json'
      },
      body: formData,
    });

    if (!response.ok) {
      console.error('Forget password failed:', response.statusText);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Forget password error:', error);
    return null;
  }
};

export const fetchPaginatedData = async function <T>(
  url: string,
  page: number = 1,
  limit: number = 50,
  token: string
): Promise<T | null> {
  try {
    if (!token) {
      console.error("Authorization token is missing");
      return null;
    }

    if (page <= 0 || limit <= 0) {
      console.error("Invalid pagination parameters: page and limit must be greater than 0");
      return null;
    }

    const queryParams = new URLSearchParams({ 
      page: page.toString(), 
      limit: limit.toString() 
    }).toString();
    const fullUrl = `${url}?${queryParams}`;

    const response = await fetch(fullUrl, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        "Cache-Control": "no-cache",
      },
    });

    if (!response.ok) {
      console.error(`Error fetching data: ${response.statusText}`);
      return null;
    }

    const data: T = await response.json();
    if (!data) {
      console.error("Invalid response structure: data is null or undefined");
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error fetching paginated data:", error);
    return null;
  }
};

export const createPatient = async (
  token: string,
  patientData: {
    planid: string,
    country: string;
    step: string;
    first_name: string;
    last_name: string;
    dob: string;
    gender: string;
    ship_to_office: string;
    bill_to_office: string;
  },
  id?: string
): Promise<CreatePatientResponse | null> => {
  try {
    if (!token) {
      console.error("Authorization token is missing");
      return null;
    }

    const formData = new FormData();
    if (id) {
      formData.append("id", id);
    }
    formData.append("country", patientData.country);
    formData.append("step", patientData.step);
    formData.append("first_name", patientData.first_name);
    formData.append("last_name", patientData.last_name);
    formData.append("dob", patientData.dob);
    formData.append("gender", patientData.gender);
    formData.append("ship_to_office", patientData.ship_to_office);
    formData.append("bill_to_office", patientData.bill_to_office);
    formData.append("plan_id", patientData.planid);

    const response = await fetch(`${API_ROUTES.PATIENT.ADD_PATIENT}`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      console.error(`Error creating patient: ${response.statusText}`);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error creating patient:", error);
    return null;
  }
};

export const submitClinicalConditions = async (
  token: string,
  payload: {
    step: string;
    id: string;
    clinical_conditions: string;
    general_notes: string;
  }
): Promise<ClinicalConditionsResponse | null> => {
  try {
    const formData = new FormData();
    formData.append("step", payload.step);
    formData.append("id", payload.id);
    formData.append("clinical_conditions", payload.clinical_conditions);
    formData.append("general_notes", payload.general_notes);

    const response = await fetch(API_ROUTES.PATIENT.ADD_CLINICAL_DATA, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      console.error("Error submitting clinical conditions:", response.statusText);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error in submitClinicalConditions:", error);
    return null;
  }
};

export const submitPatientData = async (
  token: string,
  id: string,
  records: {
    stlFile1: File;
    stlFile2: File;
    cbctFile: File;
    profileRepose: File;
    buccalRight: File;
    buccalLeft: File;
    frontalRepose: File;
    frontalSmiling: File;
    labialAnterior: File;
    occlussalLower: File;
    occlussalUpper: File;
    radioGraph1?: File | null;
    radioGraph2?: File | null;
    generalRecords?: File[];
  }
): Promise<PatientDataResponse | null> => {
  const formData = new FormData();
  formData.append("step", "step3");
  formData.append("id", id);
  formData.append("stlFile1", records.stlFile1);
  formData.append("stlFile2", records.stlFile2);
  formData.append("cbctFile", records.cbctFile);
  formData.append("profileRepose", records.profileRepose);
  formData.append("buccalRight", records.buccalRight);
  formData.append("buccalLeft", records.buccalLeft);
  formData.append("frontalRepose", records.frontalRepose);
  formData.append("frontalSmiling", records.frontalSmiling);
  formData.append("labialAnterior", records.labialAnterior);
  formData.append("occlussalLower", records.occlussalLower);
  formData.append("occlussalUpper", records.occlussalUpper);
  formData.append("radioGraph1", records.radioGraph1 || new Blob());
  formData.append("radioGraph2", records.radioGraph2 || new Blob());
  
  if (records.generalRecords && records.generalRecords.length > 0) {
    records.generalRecords.forEach((file, index) => {
      formData.append(`generalRecords[${index}]`, file);
    });
  }

  try {
    const response = await fetch(API_ROUTES.PATIENT.ADD_RECORDS_DATA, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      console.error("Error submitting patient data:", response.statusText);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error in submitPatientData:", error);
    return null;
  }
};

export const submitCasePrescription = async (
  token: string,
  id: string,
  casePrescriptionData: Record<string, unknown>,
  version: string
): Promise<CasePrescriptionResponse | null> => {
  try {
    if (!token) {
      console.error("Authorization token is missing");
      return null;
    }

    const formData = new FormData();
    formData.append("step", "step4");
    formData.append("id", id);
    formData.append("case_prescription", JSON.stringify(casePrescriptionData));
    formData.append("version", version);

    const response = await fetch(API_ROUTES.PATIENT.ADD_CASE_PERSCRIPTION, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      console.error("Error submitting case prescription:", response.statusText);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error in submitCasePrescription:", error);
    return null;
  }
};

// Fetch employees/staff data from API
export const fetchEmployees = async (
  token: string,
  page: number = 1,
  limit: number = 10
): Promise<{
  employees: any[];
  currentPage: number;
  perPage: number;
  totalItems: number;
  totalPages: number;
} | null> => {
  try {
    if (!token) {
      console.error("Authorization token is missing");
      return null;
    }
    const response = await fetch(`http://localhost:5008/api/v1/doctor/employees?page=${page}&limit=${limit}`,
      {
        method: 'GET',
        headers: {
          'accept': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      }
    );
    if (!response.ok) {
      console.error(`Error fetching employees: ${response.statusText}`);
      return null;
    }
    const data = await response.json();
    // Adjust for nested data structure
    const d = data.data || {};
    return {
      employees: d.data || [],
      currentPage: d.currentPage || 1,
      perPage: d.perPage || 10,
      totalItems: d.totalItems || 0,
      totalPages: d.totalPages || 1,
    };
  } catch (error) {
    console.error('Error fetching employees:', error);
    return null;
  }
};

// Update employee (edit)
export const updateEmployee = async (
  employeeId: string,
  data: {
    first_name: string;
    last_name: string;
    email: string;
    salutation: string;
    practice_phone_number: string;
    mobile: string;
    profession: string;
  },
  token: string
): Promise<any | null> => {
  try {
    if (!token) {
      console.error('Authorization token is missing');
      return null;
    }
    const formData = new FormData();
    formData.append('first_name', data.first_name);
    formData.append('last_name', data.last_name);
    formData.append('email', data.email);
    formData.append('salutation', data.salutation);
    formData.append('practice_phone_number', data.practice_phone_number);
    formData.append('mobile', data.mobile);
    formData.append('profession', data.profession);

    const response = await fetch(`http://localhost:5008/api/v1/doctor/employees/${employeeId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'accept': 'application/json',
      },
      body: formData,
    });
    if (!response.ok) {
      console.error(`Error updating employee: ${response.statusText}`);
      return null;
    }
    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error updating employee:', error);
    return null;
  }
};

// Create employee (add)
export const createEmployee = async (
  data: {
    first_name: string;
    last_name: string;
    email: string;
    salutation: string;
    practice_phone_number: string;
    mobile: string;
    profession: string;
  },
  token: string
): Promise<any | null> => {
  try {
    if (!token) {
      console.error('Authorization token is missing');
      return null;
    }
    const formData = new FormData();
    formData.append('first_name', data.first_name);
    formData.append('last_name', data.last_name);
    formData.append('email', data.email);
    formData.append('salutation', data.salutation);
    formData.append('practice_phone_number', data.practice_phone_number);
    formData.append('mobile', data.mobile);
    formData.append('profession', data.profession);

    // Debug: log FormData
    console.log('createEmployee called');
    for (let pair of formData.entries()) {
      console.log('FormData:', pair[0], pair[1]);
    }

    const response = await fetch('http://localhost:5008/api/v1/doctor/employees', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'accept': 'application/json',
      },
      body: formData,
    });
    if (!response.ok) {
      console.error(`Error creating employee: ${response.statusText}`);
      return null;
    }
    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error creating employee:', error);
    return null;
  }
};

// Fetch a single employee by ID
export interface EmployeeApiResponse {
  status: number;
  success: boolean;
  data: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    profile_image: string | null;
    role: string;
    salutation: string;
    practice_phone_number: string;
    mobile: string;
    profession: string;
  };
  message: string;
}

export const fetchEmployeeById = async (
  id: string | number,
  token: string
): Promise<EmployeeApiResponse['data'] | null> => {
  try {
    if (!token) {
      console.error('Authorization token is missing');
      return null;
    }
    const response = await fetch(`http://localhost:5008/api/v1/doctor/employee/${id}`, {
      method: 'GET',
      headers: {
        'accept': 'application/json',
        'Authorization': `Bearer ${token}`,
      },
    });
    if (!response.ok) {
      console.error(`Error fetching employee: ${response.statusText}`);
      return null;
    }
    const data: EmployeeApiResponse = await response.json();
    return data.data;
  } catch (error) {
    console.error('Error fetching employee:', error);
    return null;
  }
};
