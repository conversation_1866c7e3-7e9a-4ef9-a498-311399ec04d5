"use client"
import { useRouter } from "next/navigation"

interface props {
heading: string
sub?: string
href: string
content: string | React.ReactNode
border?: boolean
}

const SummaryCardNumbered: React.FC<props> = ({heading, href, content, sub, border = true}) => {
    const router = useRouter()
    return (
        <div>
            <div className={`flex flex-col gap-1  pb-4 ${border && "border-b-2 border-b-gray/50"}`}>
                <div className='flex items-center justify-between'>
                    <p className='text-base font-semibold text-dark'>{heading} <span className="text-sm font-medium text-dark">{sub}</span></p>
                    <button onClick={() => router.push(href)} className='text-orange text-sm font-medium cursor-pointer underline'>Edit</button>
                </div>
                <div className='ps-3.5'>
                    <div className='text-base'>{content}</div>
                </div>
            </div>
        </div>
    )
}

export default SummaryCardNumbered