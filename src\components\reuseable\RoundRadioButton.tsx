import React from 'react';
import { UseFormRegister, Path } from 'react-hook-form';
import { FieldValues } from 'react-hook-form';

interface RoundRadioButtonProps<T extends FieldValues> {
  id: string;
  label: string | React.ReactNode;
  value?: string;
  register: UseFormRegister<T>;
  name: string;
  defaultChecked?: boolean;
  labelClass?: string
  onClick?: React.MouseEventHandler<HTMLInputElement>;
}

const RoundRadioButton = <T extends FieldValues>({
  id,
  label,
  value,
  register,
  name,
  defaultChecked,
  labelClass,
  onClick,
}: RoundRadioButtonProps<T>) => {
  return (
    <label htmlFor={id} className='flex items-center gap-2'>
      <div className="cursor-pointer min-w-6 min-h-6 rounded-full border border-gray-400 flex items-center justify-center peer-checked:bg-white peer-checked:border-primary">
        <input
          onClick={onClick}
          type="radio"
          id={id}
          value={value}
          {...register(name as unknown as Path<T>)}
          className="peer hidden"
          defaultChecked={defaultChecked}
        />
        <div className={`w-3 h-3 rounded-full peer-checked:bg-primary`}></div>
      </div>
      {label && <span className={`${labelClass} text-gray cursor-pointer`}>{label}</span>}
    </label>
  );
};

export default RoundRadioButton;