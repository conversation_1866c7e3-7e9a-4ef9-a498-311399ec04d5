import React from 'react';
import { DataTable } from 'mantine-datatable';
import { Text, Anchor, ActionIcon } from '@mantine/core';
import editIcon from "../../../../public/svgs/icons8_edit_1 1.svg"
import Image from 'next/image';

interface InvoiceData {
  id: string;
  patientName: string;
  patientId: string;
  invoiceDate: string;
  invoiceAmount: number;
  amountDue: number;
  dueDate: string;
  daysPastDue: number;
}

const data: InvoiceData[] = Array(4).fill({
  id: '**********',
  patientName: 'Al<PERSON><PERSON>, <PERSON>',
  patientId: '25586279',
  invoiceDate: '08/10/2024',
  invoiceAmount: 8882.0,
  amountDue: 8882.0,
  dueDate: '07/11/2024',
  daysPastDue: 133,
});

const InvoiceTable = () => {
  return (
    <DataTable
      withColumnBorders
      highlightOnHover
      striped
      className="table-hover bg-white rounded-2xl"
      columns={[
        {
          accessor: 'id',
          title: 'Invoices or Documents',
          render: (row) => (
            <Anchor href={`#`} color="blue">
              {row.id}
            </Anchor>
          ),
        },
        { accessor: 'patientName', title: 'Patient(s)' },
        { accessor: 'patientId', title: 'Patient ID' },
        { accessor: 'invoiceDate', title: 'Invoice Date' },
        {
          accessor: 'invoiceAmount',
          title: 'Invoice Amount (SAR)',
          render: ({ invoiceAmount }) => invoiceAmount.toFixed(2),
        },
        {
          accessor: 'amountDue',
          title: 'Amount Due (SAR)',
          render: ({ amountDue }) => amountDue.toFixed(2),
        },
        { accessor: 'dueDate', title: 'Due Date' },
        {
          accessor: 'daysPastDue',
          title: 'Days Past Due',
          render: ({ daysPastDue }) => (
            <Text color="red">{daysPastDue}</Text>
          ),
        },
        {
          accessor: 'internalRemarks',
          title: 'Internal Remarks',
          render: () => '', // placeholder for now
        },
        {
          accessor: 'customerNotes',
          title: 'Customers Notes',
          render: () => (
            <ActionIcon variant="transparent" color="orange">
              <Image src={editIcon} alt='edit icon' width={20} height={20} />
            </ActionIcon>
          ),
        },
      ]}
      records={data}
    />
  );
}

export default InvoiceTable
