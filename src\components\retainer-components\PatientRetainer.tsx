'use client'
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useEffect, useRef, useState } from 'react'
import { z } from 'zod';
import FormWrapper from '../reuseable/FormWrapper';
import FormHeading from '../reuseable/FormHeading';
import CustomInput from '../reuseable/CustomInput';
import RoundRadioButton from '../reuseable/RoundRadioButton';
import CustomDateInput from '../reuseable/CustomDateInput';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';

const patientDataSchema = z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    gender: z.enum(["male", "female"], {
        errorMap: () => ({ message: "Gender is required" }),
    }),
    birthdate: z.coerce.date({
        errorMap: () => ({ message: "Birthdate is required and must be a valid date" }),
    }),
    country: z.enum(["saudi-arabia", "bahrain"], {
        errorMap: () => ({ message: "Country is required" }),
    }),
    shipAddress: z.enum([
        "Brny medical complex(# 1742784), Prince Talal st., Rashdiya 8680",
        "Ansam medical clinics(# 1631426), Khalid bin Alwaleed street",
        "iBraces Dental clinics(# 1434279), AlOqair street"
    ], {
        errorMap: () => ({ message: "Shipping to address is required" }),
    }),
    billAddress: z.enum([
        "iBraces Dental clinics(# 1434279), AlOqair street"
    ], {
        errorMap: () => ({ message: "Billing address is required" }),
    }),
});
type UserFormData = z.infer<typeof patientDataSchema>;
const countryOptions = [
    { value: "saudi-arabia", label: "Saudi Arabia" },
    { value: "bahrain", label: "Bahrain" },
];


const PatientRetainer = () => {
    const router = useRouter()
    const { register, handleSubmit, watch, formState: { errors }, setValue } = useForm<UserFormData>({ resolver: zodResolver(patientDataSchema), });

    const [gender, setGender] = useState<string>("")
    const [shipAddress, setShipAddress] = useState<string>("")
    const [billAddress, setBillAddress] = useState<string>("")
    const [countryOpen, setCountryOpen] = useState(false);
    const [countryLabel, setCountryLabel] = useState<string>("");
    const countryDropdownRef = useRef<HTMLDivElement>(null);
    console.log("🚀 ~ PatientRetainer ~ countryLabel:", countryLabel)


    // Click outside handler
    useEffect(() => {
        if (!countryOpen) return;
        const handleClickOutside = (event: MouseEvent) => {
            if (
                countryDropdownRef.current &&
                !countryDropdownRef.current.contains(event.target as Node)
            ) {
                setCountryOpen(false);
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => document.removeEventListener("mousedown", handleClickOutside);
    }, [countryOpen]);
    const onSubmit = (data: UserFormData): void => {
        console.log("Valid form data:", data);
        localStorage.setItem("patientData", JSON.stringify(data))
        router.push("/treatment-retainer-option");
    };


    useEffect(() => {
        localStorage.removeItem("treatmentOption")
    }, [])


    return (
        <>
            <FormWrapper onSubmit={handleSubmit(onSubmit)} onBack={() => router.back()} showCancelButton={true}>
                <div className='col-span-1 flex flex-col justify-between gap-6'>

                    <div>
                        <FormHeading text='Patient Name*' />

                        <div className='flex flex-col gap-2'>
                            <CustomInput type='text' placeholder='First Name' className='!py-3.5' register={register("firstName")} error={errors.firstName?.message} />
                            <CustomInput type='text' placeholder='Last Name' className='!py-3.5' register={register("lastName")} error={errors.lastName?.message} />
                        </div>
                    </div>


                    <div>
                        <FormHeading text='Country*' />

                        <div className="relative" ref={countryDropdownRef}>

                            <button
                                type="button"
                                className={`w-full px-4 py-3 border border-gray-300 rounded-full text-left bg-white ${errors.country ? "border-red-500" : ""}`}
                                onClick={() => setCountryOpen((open) => !open)}
                                tabIndex={0}
                            >
                                {countryOptions.find(opt => opt.value === watch("country"))?.label || "Select Country"}
                            </button>
                            {countryOpen && (
                                <ul className="absolute z-10 w-full bg-white border border-gray-300 rounded-lg mt-1 shadow">
                                    {countryOptions.map(option => (
                                        <li
                                            key={option.value}
                                            className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                            onClick={() => {
                                                setCountryOpen(false);
                                                setCountryLabel(option.label);

                                                setValue("country", option.value as "saudi-arabia" | "bahrain", { shouldValidate: true });
                                            }}
                                        >
                                            {option.label}
                                        </li>
                                    ))}
                                </ul>
                            )}
                            {errors.country && (
                                <p className="text-red-500 text-sm mt-1">{errors.country.message}</p>
                            )}
                        </div>
                    </div>

                    <div>
                        <FormHeading text='Patient Gender*' />
                        <div className='flex items-center gap-4'>
                            <RoundRadioButton
                                id='gender-male'
                                label='Male'
                                value='male'
                                name='gender'
                                onClick={(e: React.MouseEvent<HTMLInputElement>) => setGender((e.target as HTMLInputElement).value as "male")}
                                register={register}
                                defaultChecked={gender === 'male'}
                            />
                            <RoundRadioButton
                                id='gender-female'
                                label='Female'
                                value='female'
                                name='gender'
                                register={register}
                                onClick={(e: React.MouseEvent<HTMLInputElement>) => setGender((e.target as HTMLInputElement).value as "female")}
                                defaultChecked={gender === 'female'}
                            />
                        </div>
                        {errors.gender && <p className="text-red-500 text-sm mt-1">{errors.gender.message}</p>}
                    </div>


                    <div>
                        <FormHeading text="Date of Birth*" />
                        <div className="">
                            <CustomDateInput
                                value={watch("birthdate")}
                                register={register("birthdate")}
                                id="birthDate"
                                name="birthdate"
                                className=''
                                minDate={new Date(1900, 0, 1)} // January 1, 1900
                                maxDate={new Date()} // Today
                            />
                        </div>
                        {
                            <p className="text-red-500 text-sm mt-1">
                                {errors?.birthdate?.message}
                            </p>
                        }
                    </div>

                </div>


                <div className='col-span-1'>
                    <div className='flex flex-col gap-4'>
                        <div>
                            <FormHeading text='Ship To Office*' />

                            <div className='flex flex-col gap-5'>
                                <RoundRadioButton
                                    id='iBraces Dental clinics(# 1434279), AlOqair street'
                                    label='iBraces Dental clinics(# 1434279), AlOqair street'
                                    value='iBraces Dental clinics(# 1434279), AlOqair street'
                                    name='shipAddress'
                                    defaultChecked={shipAddress == 'iBraces Dental clinics(# 1434279), AlOqair street'}
                                    onClick={(e: React.MouseEvent<HTMLInputElement>) => setShipAddress((e.target as HTMLInputElement).value as "iBraces Dental clinics(# 1434279), AlOqair street")}
                                    register={register}
                                />
                                <RoundRadioButton
                                    id='Ansam medical clinics(# 1631426), Khalid bin Alwaleed street'
                                    label='Ansam medical clinics(# 1631426), Khalid bin Alwaleed street'
                                    value='Ansam medical clinics(# 1631426), Khalid bin Alwaleed street'
                                    name='shipAddress'
                                    defaultChecked={shipAddress == 'Ansam medical clinics(# 1631426), Khalid bin Alwaleed street'}
                                    onClick={(e: React.MouseEvent<HTMLInputElement>) => setShipAddress((e.target as HTMLInputElement).value as "Ansam medical clinics(# 1631426), Khalid bin Alwaleed street")}
                                    register={register}
                                />
                                <RoundRadioButton
                                    id='Brny medical complex(# 1742784), Prince Talal st., Rashdiya 8680'
                                    label='Brny medical complex(# 1742784), Prince Talal st., Rashdiya 8680'
                                    value='Brny medical complex(# 1742784), Prince Talal st., Rashdiya 8680'
                                    name='shipAddress'
                                    defaultChecked={shipAddress == 'Brny medical complex(# 1742784), Prince Talal st., Rashdiya 8680'}
                                    onClick={(e: React.MouseEvent<HTMLInputElement>) => setShipAddress((e.target as HTMLInputElement).value as "Brny medical complex(# 1742784), Prince Talal st., Rashdiya 8680")}
                                    register={register}
                                />
                            </div>
                            {<p className="text-red-500 text-sm mt-1">{errors?.shipAddress?.message}</p>}
                        </div>

                        <div>
                            <FormHeading text='Bill To Office*' />
                            <div className='flex flex-col gap-5'>
                                <RoundRadioButton
                                    id='bill-iBraces Dental clinics(# 1434279), AlOqair street'
                                    label='iBraces Dental clinics(# 1434279), AlOqair street'
                                    value='iBraces Dental clinics(# 1434279), AlOqair street'
                                    name='billAddress'
                                    defaultChecked={billAddress == 'iBraces Dental clinics(# 1434279), AlOqair street'}
                                    onClick={(e: React.MouseEvent<HTMLInputElement>) => setBillAddress((e.target as HTMLInputElement).value as "iBraces Dental clinics(# 1434279), AlOqair street")}
                                    register={register}
                                />
                            </div>
                            {<p className="text-red-500 text-sm mt-1">{errors?.billAddress?.message}</p>}
                        </div>

                    </div>
                </div>
            </FormWrapper>
        </>
    )
}

export default PatientRetainer
