'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import StaffTable, { StaffMember } from './StaffTable';
import getAndDecryptCookie from '@/app/lib/auth';
import { fetchEmployees } from '@/utils/ApisHelperFunction';

const Staff = () => {
  const router = useRouter();
  
  // API integration state
  const [allStaffData, setAllStaffData] = useState<StaffMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      try {
        const token = getAndDecryptCookie('AccessToken');
        if (!token) {
          setError('Authentication token missing. Please log in again.');
          setLoading(false);
          return;
        }
        const employees = await fetchEmployees(token);
        if (employees && Array.isArray(employees)) {
          setAllStaffData(employees);
        } else {
          setError('Failed to fetch staff data.');
        }
      } catch (err) {
        setError('An error occurred while fetching staff data.');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  
  // Use useMemo for paginated data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return allStaffData.slice(startIndex, endIndex);
  }, [currentPage, itemsPerPage, allStaffData]);
  
  // Calculate total pages
  const totalPages = useMemo(() => {
    return Math.ceil(allStaffData.length / itemsPerPage);
  }, [allStaffData.length, itemsPerPage]);
  
  // Validate current page
  useEffect(() => {
    if (currentPage > totalPages && totalPages > 0) {
      setCurrentPage(totalPages);
    }
  }, [totalPages]);

  // Navigation handlers
  const handleAddStaff = () => {
    router.push('/account/staff/staff-details?mode=add');
  };

  const handleViewStaff = (id: string) => {
    router.push(`/account/staff/staff-details?mode=view&id=${id}`);
  };

  const handleEditStaff = (id: string) => {
    router.push(`/account/staff/staff-details?mode=edit&id=${id}`);
  };

  // Pagination handlers
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (items: number) => {
    setItemsPerPage(items);
    setCurrentPage(1);
  };

  // Add the handler function inside the Staff component
  const handleStatusChange = (id: string, newStatus: 'active' | 'inactive') => {
    // Create a new array with the updated status
    const updatedStaffData = allStaffData.map(staff => {
      if (staff.id === id) {
        return { ...staff, status: newStatus };
      }
      return staff;
    });
    
    // In a real application, you would make an API call here
    console.log(`Updating staff ${id} status to ${newStatus}`);
    
    // Update the state with the new array
    // If you're using a state management library or API, you'd update the data there instead
    setAllStaffData(updatedStaffData);
  };

  return (
    <div className="w-full bg-white p-3 rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Staff Management</h1>
        <button
          onClick={handleAddStaff}
          className="bg-primary text-white ps-2 pe-4 py-2 rounded-full font-medium flex items-center"
        >
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            className="h-5 w-5" 
            viewBox="0 0 20 20" 
            fill="currentColor"
          >
            <path 
              fillRule="evenodd" 
              d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" 
              clipRule="evenodd" 
            />
          </svg>
          Add Staff
        </button>
      </div>

      {loading ? (
        <div className="text-center py-8">Loading staff data...</div>
      ) : error ? (
        <div className="text-center text-red-500 py-8">{error}</div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <StaffTable 
            data={paginatedData} 
            currentPage={currentPage}
            totalItems={allStaffData.length}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            onViewStaff={handleViewStaff}
            onEditStaff={handleEditStaff}
            onStatusChange={handleStatusChange}
          />
        </div>
      )}
    </div>
  );
};

export default Staff;
