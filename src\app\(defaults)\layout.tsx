'use client'
import '@/app/globals.css';
import Sidebar from '@/components/reuseable/Sidebar';
import { useState } from 'react';

export default function DefaultLayout({ children }: { children: React.ReactNode }) {
    const [isOpen, setIsOpen] = useState(true);

    const toggleSidebar = () => {
        setIsOpen(!isOpen);
    };

    return (
        <div className="flex bg-[#f5f5f5] min-h-screen">
            {/* Sidebar remains fixed */}
            <div
                className={`fixed top-0 left-0 h-screen transition-all duration-300 ${
                    isOpen ? 'w-72' : 'w-20'
                }`}
            >
                <Sidebar isOpen={isOpen} toggleSidebar={toggleSidebar} />
            </div>
            {/* Main content adjusts dynamically */}
            <main
                className={`flex-1 min-h-screen overflow-y-auto flex flex-col transition-all duration-300 ${
                    isOpen ? 'ml-72' : 'ml-20'
                }`}
            >
                {children}
            </main>
        </div>
    );
}