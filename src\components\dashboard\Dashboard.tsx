'use client';

import Header from '../reuseable/Header';
import TableComponent from '../reuseable/ReuseableTable';
import { useState, useEffect } from 'react';
import plusIcon from "../../../public/svgs/plus-sign.svg";
import { useRouter } from 'next/navigation';
import { columns } from '../../constants/constants';
import { PatientData } from '@/types/types';
import { API_ROUTES } from '@/utils/ApiRoutes';
import { fetchPaginatedData } from '@/utils/ApisHelperFunction';
import getAndDecryptCookie from '@/app/lib/auth';
import Image from 'next/image';

interface PatientsApiResponse {
    status: number;
    success: boolean;
    data: {
        data: PatientData[];
        pagination: {
            page: number;
            limit: number;
            totalPages: number;
            totalItems: number;
        };
    };
    message: string;
}

// Add this interface at the top of the file
interface TransformedPatientData {
    id: string;
    patient: {
        image?: string;
        name: string;
        id: string;
    };
    startDate: string;
    treatmentOption: string;
    status: string;
    notes: string;
    daysSinceLastUpdate: number;
    country: string;
}

const Dashboard = () => {
    const router = useRouter();
    const token = getAndDecryptCookie('AccessToken') || '';

    const [patients, setPatients] = useState<PatientData[]>([]);
    // Update this line with proper type
    const [filteredData, setFilteredData] = useState<TransformedPatientData[]>([]);
    const [loading, setLoading] = useState(false);
    console.log("🚀 ~ Dashboard ~ loading:", loading)
    const [pagination, setPagination] = useState({
        page: 1,
        limit: 10,
        totalPages: 0,
        totalItems: 0,
    });
    const [activeTab, setActiveTab] = useState('treatment');
    const [searchQuery, setSearchQuery] = useState('');

    const transformPatientDataToTableRow = (patientData: PatientData[]) => {
        return patientData.map((patient) => ({
            id: patient?.id?.toString(),
            patient: {
                image: patient?.profileRepose || undefined,
                name: `${patient?.first_name} ${patient?.last_name}`,
                id: patient?.uuid ? `#${patient?.uuid}` : '#',
            },
            startDate: new Date(patient?.created_at).toLocaleDateString(),
            treatmentOption: patient?.plan_name || 'N/A',
            status: patient?.status || 'Not Started Yet',
            notes: patient?.general_notes || 'No notes',
            daysSinceLastUpdate: Math.floor((new Date().getTime() - new Date(patient?.updated_at).getTime()) / (1000 * 60 * 60 * 24)),
            country: patient?.country || 'Not available',
        }));
    };

    const fetchPatients = async (page: number, limit: number) => {
        setLoading(true);
        try {
            const response = await fetchPaginatedData<PatientsApiResponse>(
                `${API_ROUTES.PATIENT.GET_ALL_PATIENTS}`,
                page,
                limit,
                token
            );

            if (response?.data) {
                setPatients(response.data.data);
                setPagination({
                    page: response.data.pagination.page,
                    limit: response.data.pagination.limit,
                    totalPages: response.data.pagination.totalPages,
                    totalItems: response.data.pagination.totalItems,
                });
            }
        } catch (error) {
            console.error("Error fetching patients:", error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchPatients(pagination.page, pagination.limit);
    }, [pagination.page, pagination.limit]);

    useEffect(() => {
        const tabData = getDataByTab(activeTab);
        setFilteredData(transformPatientDataToTableRow(tabData));
    }, [patients, activeTab]);

    const getDataByTab = (tab: string): PatientData[] => {
        switch (tab) {
            case 'treatment':
                return patients.filter((patient) => patient?.status !== null && patient?.status !== 'archived');
            case 'action':
                return patients.filter((patient) => patient?.status === null);
            case 'archived':
                return patients.filter((patient) => patient?.status === 'archived' || !patient?.is_active);
            default:
                return patients;
        }
    };

    const handleTabChange = (tab: string) => {
        setActiveTab(tab);
        setSearchQuery('');
    };

    const handleSearchChange = (value: string) => {
        setSearchQuery(value);
        const currentData = getDataByTab(activeTab);

        if (!value.trim()) {
            setFilteredData(transformPatientDataToTableRow(currentData));
            return;
        }

        const searchTerm = value.toLowerCase().trim();
        const filtered = currentData.filter((patient) => {
            const fullName = `${patient?.first_name} ${patient?.last_name}`.toLowerCase();
            return (
                fullName.includes(searchTerm) ||
                patient?.id.toString().includes(searchTerm) ||
                patient?.email?.toLowerCase().includes(searchTerm) ||
                patient?.plan_name?.toLowerCase().includes(searchTerm) ||
                patient?.status?.toLowerCase().includes(searchTerm) ||
                patient?.general_notes?.toLowerCase().includes(searchTerm) ||
                patient?.country?.toLowerCase().includes(searchTerm)
            );
        });

        setFilteredData(transformPatientDataToTableRow(filtered));
    };

    const handlePageChange = (newPage: number) => {
        setPagination((prev) => ({ ...prev, page: newPage }));
    };

    const handleItemsPerPageChange = (newLimit: number) => {
        setPagination((prev) => ({ ...prev, limit: newLimit, page: 1 }));
    };

    const tabs = [
        { value: 'treatment', label: 'In Treatment', count: getDataByTab('treatment').length },
        { value: 'action', label: 'Action Required', count: getDataByTab('action').length },
        { value: 'archived', label: 'Archived', count: getDataByTab('archived').length },
    ];

    return (
        <div className="min-h-[80svh] bg-[#f5f5f5] px-4 py-4">
            <Header onSearchChange={handleSearchChange} searchValue={searchQuery} />

            <div className="rounded-lg pt-6">
                <div className="flex lg:flex-row flex-col items-center lg:justify-between mb-4">
                    <div className="flex space-x-3">
                        {tabs.map((tab) => (
                            <button
                                key={tab.value}
                                type="button"
                                onClick={() => handleTabChange(tab.value)}
                                className={`rounded-full px-4 py-2 transition-all cursor-pointer duration-200 font-medium ${
                                    activeTab === tab.value
                                        ? 'bg-[#EB6309] text-white hover:bg-[#D45A08]'
                                        : 'bg-white text-gray-700 hover:bg-gray-100'
                                }`}
                            >
                                {tab.label} ({tab.count > 99 ? '99+' : tab.count})
                            </button>
                        ))}
                    </div>

                    <div className='flex lg:justify-start justify-end gap-2 lg:mb-0 mb-2'>
                        <button
                            onClick={() => router.push("/patient-data")}
                            className={`rounded-full cursor-pointer flex gap-1 items-center px-4 py-2 transition-all duration-200 font-medium bg-[#EB6309] text-white hover:bg-[#D45A08]`}
                        >
                            <span>Add New Patient</span>
                            <Image src={plusIcon} width={1000} height={1000} alt="Plus Icon" className="w-5 h-5" />
                        </button>
                        <button
                            onClick={() => router.push("/patient-retainer")}
                            className={`rounded-full cursor-pointer flex gap-1 items-center px-4 py-2 transition-all duration-200 font-medium bg-[#EB6309] text-white hover:bg-[#D45A08]`}
                        >
                            <span>Add New Retainer</span>
                            <Image src={plusIcon} width={1000} height={1000} alt="Plus Icon" className="w-5 h-5" />
                        </button>
                    </div>
                </div>

                <div className="tab-content">
                    {filteredData.length > 0 ? (
                        <TableComponent
                            columns={columns}
                            data={filteredData}
                            pagination={{
                                currentPage: pagination.page,
                                totalItems: pagination.totalItems,
                                itemsPerPage: pagination.limit,
                                totalPages: pagination.totalPages,
                                onPageChange: handlePageChange,
                                onItemsPerPageChange: handleItemsPerPageChange,
                            }}
                        />
                    ) : (
                        <div className="text-center text-gray-500 py-4">No results found</div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default Dashboard;