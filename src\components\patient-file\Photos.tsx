"use client"
import { useRef } from "react";
import { StaticImport } from "next/dist/shared/lib/get-img-props";
import Image from "next/image";
import closeIcon from "../../../public/svgs/icons8_close 1.svg";
import addIcon from "../../../public/svgs/icons8_add 1.svg";
import buccalLeft from "../../../public/svgs/buccal-left.svg";
import logo from "../../../public/images/logo.png";
import buccalRight from "../../../public/svgs/buccal-right.8f4707a1..svg";
import frontalRepose from "../../../public/svgs/frontal-repose.5f430b49..svg";
import frontalSmiling from "../../../public/svgs/frontal-smiling.6c08f65f..svg";
import labialAnterior from "../../../public/svgs/labial-anterior.9cf4e2c6..svg";
import occlussalLower from "../../../public/svgs/occlussal-lower.3be1bcdf..svg";
import occlussalUpper from "../../../public/svgs/occlussal-upper.cd664940..svg";
import profileRepose from "../../../public/svgs/profile-repose.cf7b4b65..svg";
import socialSmile from "../../../public/svgs/social-smile.ab9bc0e3..svg";
import xRayLeft from "../../../public/svgs/bg-xray-left 1.svg";
import xRayRight from "../../../public/svgs/bg-xray-right 1.svg";
import { TabName } from "./RefinementModel";

interface DentalPhoto {
    name: string;
    file: File | null;
}

interface props {
    setTab: (val: TabName, force: boolean) => void
    handleFinalSubmit: () => void
    onClose: () => void
    setPhotosData: React.Dispatch<React.SetStateAction<DentalPhoto[]>>
    photosData: DentalPhoto[]
    setRadiographData: React.Dispatch<React.SetStateAction<{
        radiograph1: File | null;
        radiograph2: File | null;
    }>>
    radiographData: {
        radiograph1: File | null;
        radiograph2: File | null;
    }
}

interface Placeholder {
    name: string;
    placeholder: StaticImport;
}

const patientPhotos: Placeholder[] = [
    { name: "profileRepose", placeholder: profileRepose },
    { name: "frontalRepose", placeholder: frontalRepose },
    { name: "frontalSmiling", placeholder: frontalSmiling },
    { name: "occlussalUpper", placeholder: occlussalUpper },
    { name: "socialSmile", placeholder: socialSmile },
    { name: "occlussalLower", placeholder: occlussalLower },
    { name: "buccalRight", placeholder: buccalRight },
    { name: "labialAnterior", placeholder: labialAnterior },
    { name: "buccalLeft", placeholder: buccalLeft },
];

const Photos: React.FC<props> = ({ 
    handleFinalSubmit, 
    onClose, 
    setPhotosData, 
    photosData,
    setRadiographData,
    radiographData
}) => {
    // Use photosData directly from props
    const handlePhotoFileChange = (e: React.ChangeEvent<HTMLInputElement>, name: string) => {
        const files = Array.from(e.target.files || []);
        const newPhotos = photosData.map(photo => 
            photo.name === name ? { ...photo, file: files[0] } : photo
        );
        setPhotosData(newPhotos);
    };

    const getImage = (photo: Placeholder): StaticImport | string => {
        const dentalPhoto = photosData.find((p) => p.name === photo.name);
        return dentalPhoto?.file ? URL.createObjectURL(dentalPhoto.file) : photo.placeholder;
    };

    const handleDeletePhoto = (name: string) => {
        const newPhotos = photosData.map(photo => 
            photo.name === name ? { ...photo, file: null } : photo
        );
        setPhotosData(newPhotos);
    }

    const handleRadiographChange1 = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        const file = files[0];
        if (file) {
            setRadiographData({
                ...radiographData,
                radiograph1: file
            });
        }
    };
    
    const handleRadiographChange2 = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        const file = files[0];
        if (file) {
            setRadiographData({
                ...radiographData,
                radiograph2: file
            });
        }
    };

    const removeRadioGraph = (ind: number) => {
        if (ind === 1) {
            setRadiographData({
                ...radiographData,
                radiograph1: null
            });
        } else {
            setRadiographData({
                ...radiographData,
                radiograph2: null
            });
        }
    }

    const radiograph1ref = useRef<HTMLInputElement | null>(null)
    const radiograph2ref = useRef<HTMLInputElement | null>(null)

    return (
        <div className="flex-grow flex flex-col">
            <div className="flex-grow flex flex-col justify-between">
                <div className="grid grid-cols-3">
                    {patientPhotos.map((photo, index) => 
                        photo.name === "socialSmile" ? (
                            <div key={index} className="bg-white rounded-[10px] flex flex-col items-center justify-center p-8">
                                <Image
                                    width={150}
                                    height={150}
                                    src={logo}
                                    alt="Logo"
                                    className="w-[250px] h-[250px] object-contain"
                                />
                            </div>
                        ) : (
                            <label
                                htmlFor={`upload-${photo.name}`}
                                key={index}
                                className={`bg-white relative cursor-pointer rounded-[10px] ${typeof getImage(photo) === "string" ? "p-0" : "p-8"} flex flex-col items-center justify-center`
                                }
                            >
                                        <Image
                                            width={typeof getImage(photo) === "string" ? 250 : 150}
                                            height={typeof getImage(photo) === "string" ? 250 : 150}
                                            src={getImage(photo) as StaticImport}
                                            alt={photo.name}
                                            className={typeof getImage(photo) === "string" ? "w-[150px] h-[150px] aspect-[1/1] object-cover rounded-[inherit]" : "w-[150px] h-[150px]"}
                                        />
                                        <div className="flex justify-end w-full absolute bottom-[3%] right-[3%]">
                                            {typeof getImage(photo) === "string" ? (
                                                <button
                                                    onClick={(e) => {
                                                        e.stopPropagation()
                                                        handleDeletePhoto(photo.name)
                                                    }}
                                                    type="button"
                                                    className="bg-danger/50 z-50 text-white rounded-full p-1 w-5 h-5 flex items-center justify-center"
                                                >
                                                    <Image src={closeIcon} alt="Cross icon" className="w-5 h-5 cursor-pointer" />
                                                </button>
                                            ) : (
                                                <>
                                                    <Image src={addIcon} alt="Upload icon" className="w-6 h-6" />
                                                    <input
                                                        id={`upload-${photo.name}`}
                                                        type="file"
                                                        accept="image/*"
                                                        onChange={(e) => handlePhotoFileChange(e, photo.name)}
                                                        className="hidden"
                                                    />
                                                </>
                                            )}
                                        </div>
                                    </label>
                                )
                        )}
                </div>

                <div className="grid grid-cols-2">

                    <div onClick={() => radiograph1ref?.current?.click()} className="cursor-pointer bg-white relative rounded-[10px] p-8 flex flex-col items-center justify-center">
                        <div className="relative w-full h-36">
                            {radiographData.radiograph1 ? (
                                <Image
                                    src={URL.createObjectURL(radiographData.radiograph1)}
                                    alt="Uploaded radiograph 1"
                                    fill
                                    className="w-full aspect-[1/1] object-cover rounded-[inherit]"
                                />
                            ) : (
                                <Image fill src={xRayLeft} alt="X-ray left placeholder" />
                            )}
                        </div>
                        <div className="flex justify-end w-full absolute bottom-[6%] right-[5%]">
                            {radiographData.radiograph1 ? (
                                <button
                                    onClick={() => removeRadioGraph(1)}
                                    type="button"
                                    className="bg-danger/50 text-white rounded-full p-1 w-5 h-5 flex items-center justify-center"
                                >
                                    <Image src={closeIcon} width={1000} height={1000} alt="Cross icon" className="w-5 h-5 cursor-pointer" />
                                </button>
                            ) : (
                                <>
                                    <label htmlFor="file-upload-1" className="cursor-pointer">
                                        <Image
                                            width={1000}
                                            height={1000}
                                            src={addIcon} alt="Upload icon" className="w-8 h-8" />
                                    </label>
                                    <input
                                        ref={radiograph1ref}
                                        id="file-upload-1"
                                        type="file"
                                        accept="image/*"
                                        onChange={(e) => handleRadiographChange1(e)}
                                        className="hidden"
                                    />
                                </>
                            )}
                        </div>
                    </div>
                    <div onClick={() => radiograph2ref?.current?.click()} className="cursor-pointer bg-white relative rounded-[10px] p-8 flex flex-col items-center justify-center">
                        <div className="relative w-full h-36">
                            {radiographData.radiograph2 ? (
                                <Image
                                    src={URL.createObjectURL(radiographData.radiograph2)}
                                    alt="Uploaded radiograph 2"
                                    fill
                                    className="w-full aspect-[1/1] object-cover rounded-[inherit]"
                                />
                            ) : (
                                <Image fill src={xRayRight} alt="X-ray right placeholder" />
                            )}
                        </div>

                        <div className="flex justify-end w-full absolute bottom-[6%] right-[5%]">
                            {radiographData.radiograph2 ? (
                                <button
                                    onClick={() => removeRadioGraph(2)}
                                    type="button"
                                    className="bg-danger/50 text-white rounded-full p-1 w-5 h-5 flex items-center justify-center"
                                >
                                    <Image src={closeIcon} alt="Cross icon" className="w-5 h-5 cursor-pointer" />
                                </button>
                            ) : (
                                <>
                                    <label htmlFor="file-upload-2" className="cursor-pointer">
                                        <Image src={addIcon} alt="Upload icon" className="w-8 h-8" />
                                    </label>
                                    <input
                                        ref={radiograph2ref}
                                        id="file-upload-2"
                                        type="file"
                                        accept="image/*"
                                        onChange={(e) => handleRadiographChange2(e)}
                                        className="hidden"
                                    />
                                </>
                            )}
                        </div>
                    </div>

                </div>


                <div className="flex items-center justify-end gap-3 my-4">
                    <button onClick={() => onClose()} className="flex items-center justify-center py-2 cursor-pointer rounded-full border border-gray min-w-[120px]" type="button">
                        <span className="font-semibold text-lg text-dark">Cancel</span>
                    </button>
                    <button onClick={handleFinalSubmit} className="flex items-center justify-center py-2.5 cursor-pointer rounded-full bg-primary min-w-48 hover:bg-[#D45A08] transition" type="button">
                        <span className="font-semibold text-base text-white">Next</span>
                    </button>
                </div>

            </div>


        </div >
    )
}


export default Photos