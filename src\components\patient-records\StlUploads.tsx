"use client";
import React from "react";
import UploadFile from "./UploadFile";
import { UseFormRegister, UseFormSetValue, UseFormWatch } from "react-hook-form";
import type { ScanFormData } from "./PatientRecords"

interface Props {
    register: UseFormRegister<ScanFormData>;
    watch: UseFormWatch<ScanFormData>
    setValue: UseFormSetValue<ScanFormData>
    decideLater: boolean;
}

const StlUploads: React.FC<Props> = ({ decideLater, watch, setValue, register }) => {



    return (
        <div className="grid grid-cols-2 gap-2">
            <div className="flex flex-col gap-2">
                <span className={`cursor-pointer !text-dark font-semibold`}>Upper</span>
                <UploadFile register={register} setValue={setValue} watch={watch} name={"stlFile1" as keyof ScanFormData} disabled={decideLater} />
            </div>
            <div className="flex flex-col gap-2">
                <span className={`cursor-pointer !text-dark font-semibold`}>Lower</span>
                <UploadFile register={register} setValue={setValue} watch={watch} name={"stlFile2" as keyof ScanFormData} disabled={decideLater} />
            </div>
        </div>
    );
};

export default StlUploads;