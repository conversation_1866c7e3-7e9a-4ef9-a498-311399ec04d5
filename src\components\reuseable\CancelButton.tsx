"use client"
import '@/app/globals.css';


interface props {
    text?: string
    onClick?: () => void

}

const CancelButton: React.FC<props> = ({ text = "Cancel", onClick }) => {
    return (
        <button
            onClick={onClick} 
            className="flex items-center justify-center gap-2 py-4 cursor-pointer rounded-full border border-danger min-w-[120px]">
            <span className="font-semibold text-lg text-danger">{text}</span>
        </button>
    )
}

export default CancelButton