'use client'

import React from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import Image from 'next/image'
import logo from '../../../public/images/logo1.png'
import CustomInput from '../reuseable/CustomInput'
import { useRouter } from 'next/navigation'
import '../../app/globals.css'
import { toast } from 'react-toastify'
import SquareCheckBox from '../reuseable/SquareCheckBox'
import { loginUser } from '@/utils/ApisHelperFunction'
import { storeToken, Encrytion } from '@/app/lib/auth' // Import the cookie functions

// Define Zod schema
const loginSchema = z.object({
    id: z.string().min(1, 'Username/Email is required'),
    password: z.string().min(1, 'Password is required'),
    remember: z.boolean().optional()
})

type LoginFormValues = z.infer<typeof loginSchema>

const Login = () => {
    const navigate = useRouter()
    const {
        register,
        handleSubmit,
        formState: { errors }
    } = useForm<LoginFormValues>({
        resolver: zodResolver(loginSchema),
        defaultValues: {
            id: '',
            password: '',
            remember: false
        }
    })

    const onSubmit = async (data: LoginFormValues) => {
        try {
            // Call the login API and await the result
            if (data.remember === true) {
                const result = await loginUser(data.id, data.password, data.remember)
                if (result && result.success) {
                    console.log('Login successful:', result);
                    toast.success(result.message || 'Login successful');
                    if (result.data?.accessToken) {
                        // Encrypt sensitive data before storing in cookies
                        const encryptedToken = Encrytion(result.data.accessToken);

                        // Pass the remember me state to the storeToken function
                        storeToken('AccessToken', encryptedToken, data.remember);

                        // Store user role for authorization checks
                        if (result.data.user?.role) {
                            const encryptedRole = Encrytion(String(result.data.user.role));
                            storeToken('Role', encryptedRole, data.remember);
                        }

                        // Store email for user identification if needed
                        if (result.data.user?.email) {
                            const encryptedEmail = Encrytion(String(result.data.user.email));
                            storeToken('Email', encryptedEmail, data.remember);
                        }

                        // Navigate to dashboard on success
                        navigate.push('/dashboard');
                    }
                } else {
                    console.error('Login failed:', result);
                    toast.error(result?.message || 'Login failed');
                }
            } else {
                const result = await loginUser(data.id, data.password)
                if (result && result.success) {
                    console.log('Login successful:', result);
                    toast.success(result.message || 'Login successful');
                    if (result.data?.accessToken) {
                        // Encrypt sensitive data before storing in cookies
                        const encryptedToken = Encrytion(result.data.accessToken);

                        // Store the token without remember me
                        storeToken('AccessToken', encryptedToken, false);

                        // Store user role for authorization checks
                        if (result.data.user?.role) {
                            const encryptedRole = Encrytion(String(result.data.user.role));
                            storeToken('Role', encryptedRole, false);
                        }

                        // Store email for user identification if needed
                        if (result.data.user?.email) {
                            const encryptedEmail = Encrytion(String(result.data.user.email));
                            storeToken('Email', encryptedEmail, false);
                        }
                        navigate.push('/dashboard');
                    }
                } else {
                    console.error('Login failed:', result);
                    toast.error(result?.message || 'Login failed');
                }
            }


        } catch (error) {
            toast.error('An unexpected error occurred');
            console.log(error)
        }
    }

    const handleforgetpassword = () => {
        navigate.push('/forgot-password')
    }

    return (
        <>
            <div className="min-h-screen authbackground bg-cover bg-center bg-no-repeat flex items-center justify-center relative overflow-hidden">
                {/* Background Aligners */}
                {/* <Image
                    src={upperteeths}
                    alt="Aligner Top Left"
                    width={1000}
                    height={1000}
                    className="absolute top-0 left-0 max-lg:max-w-sm lg:w-[35%] xl:w-[38%] 2xl:w-[42%] max-md:hidden"
                />
                <Image
                    src={lowerteeths}
                    alt="Aligner Bottom Right"
                    width={1000}
                    height={1000}
                    className="absolute bottom-0 right-0 max-lg:max-w-sm lg:w-[35%] xl:w-[38%] 2xl:w-[42%] max-md:hidden"
                /> */}

                {/* Login Box */}
                <div className="w-[30%] max-md:w-[80%] md:w-[50%] lg:w-[40%] xl:w-[30%] 2xl:w-[29%] z-30">
                    <div className="xl:mb-3 lg:mb-5 2xl:mb-14">
                        <Image src={logo} alt="Aligners Logo" className="mx-auto mb-2 w-44 xl:w-52" width={1000} height={1000} />
                    </div>
                    <div className='z-10 w-full 2xl:p-[50px] lg:p-[25px] p-[20px] rounded-[40px] bg-[#4444430F] text-center border border-[#44444321] backdrop-blur-sm'>
                        <h1 className="text-[38px] max-sm:text-[26px] max-md:text-[28px] md:text-[32px] font-bold text-[#444443] leading-[45px] pb-[20px] lg:pb-[8px] z-40">
                            Welcome to the <br />
                            <span className="text-[38px] max-sm:text-[26px] max-md:text-[28px] md:text-[32px] font-bold text-[#444443] leading-[45px]">Graphy<sup>®</sup> Doctor Site</span>
                        </h1>
                        <form onSubmit={handleSubmit(onSubmit)} className=" text-left pt-4  z-40">
                            <div className='space-y-2'>
                                <CustomInput
                                    className='!px-5 !py-[16px] !2xl:py-[18px] lg:py-3'
                                    type="text"
                                    placeholder="Username/Email"
                                    register={register('id')}
                                    error={errors.id?.message}
                                />
                                <CustomInput
                                    className='!px-5 !py-[16px] !2xl:py-[18px] lg:py-3'
                                    type="password"
                                    placeholder="Password"
                                    register={register('password')}
                                    error={errors.password?.message}
                                />
                                <SquareCheckBox
                                    id="remember"
                                    label="Remember username/email"
                                    register={register('remember')}
                                />
                            </div>
                            <div className='pt-5 lg:pt-3 xl:pt-9'>
                                <button
                                    type="submit"
                                    className={`w-full py-4 lg:py-3 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition cursor-pointer font-medium`}
                                >
                                    Login
                                </button>
                            </div>
                        </form>

                        <div className="xl:mt-[8px] 2xl:mt-[14px]">
                            <button className="text-sm text-[#F00] hover:underline cursor-pointer" onClick={handleforgetpassword}>
                                Forgot Username or Password?
                            </button>
                        </div>

                    </div>

                </div>
            </div>
            <div className='absolute bottom-1 w-full'>
                <p className="mt-auto text-xs mx-auto w-fit text-gray-400">
                    © 2005–2025 Graphy Inc. All rights reserved.
                </p>
            </div>
        </>
    )
}

export default Login
