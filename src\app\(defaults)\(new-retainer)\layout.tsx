"use client"
import '@/app/globals.css';
import BreadCrumbs from '@/components/layouts/BreadCrumb';
import Header from '@/components/reuseable/Header';
import { usePathname } from "next/navigation";


const trailNumbers: Record<string, number> = {
    "patient-data": 1,
    "treatment-retainer-option": 2,
    "retainer-info": 3,
    "patient-retainer-record": 4,
};

export default function DefaultLayout({ children }: { children: React.ReactNode }) {
    const location = usePathname()
    console.log("location", location)
    return (
        <div className="py-4 px-6 flex-grow flex flex-col">
            <Header onSearchChange={() => { }} searchValue={""} />
            <div className='py-6 flex-grow flex flex-col'>
                <div className='flex max-xl:flex-col min-xl:justify-between min-xl:items-center'>
                    <div className='flex justify-start'>
                        <h3 className='font-bold text-2xl text-dark min-[1220px]:mb-0 mb-4'>Add New Retainer</h3>
                    </div>
                    <div className='flex justify-end'>
                        <BreadCrumbs trailNumbers={trailNumbers} />
                    </div>
                </div>
                <div className='flex flex-col flex-grow '>
                    {children}
                </div>
            </div>
        </div>
    );
}