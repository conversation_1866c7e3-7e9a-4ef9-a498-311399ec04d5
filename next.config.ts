import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  async redirects() {
    return [
      {
        source: "/",
        destination: "/login",
        permanent: true, // Set to true if this is a permanent redirect
      },
    ];
  },
  images: {
    domains: ["ui-avatars.com", "localhost"], // Add the external domain here
  },
  // devIndicators: {
  // buildActivity: false, // Disable the build activity indicator
  // },
};

export default nextConfig;
