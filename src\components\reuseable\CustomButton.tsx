import React from 'react'

interface CustomButtonProps {
  type?: 'button' | 'submit' | 'reset'
  onClick?: () => void
  children: React.ReactNode
  className?: string
  disabled?: boolean
}

const CustomButton: React.FC<CustomButtonProps> = ({
  type = 'button',
  onClick,
  children,
  className = '',
  disabled = false,
}) => {
  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`w-full py-2 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition cursor-pointer ${className} ${
        disabled ? 'opacity-50' : ''
      }`}
    >
      {children}
    </button>
  )
}

export default CustomButton