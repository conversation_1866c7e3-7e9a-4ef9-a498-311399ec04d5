import React, { useState, useEffect } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import Image, { StaticImageData } from 'next/image';
import { DentalPhoto } from '../patient-records/PatientRecords';

import buccalLeft from "../../../public/svgs/buccal-left.svg";
import buccalRight from "../../../public/svgs/buccal-right.8f4707a1..svg";
import frontalRepose from "../../../public/svgs/frontal-repose.5f430b49..svg";
import frontalSmiling from "../../../public/svgs/frontal-smiling.6c08f65f..svg";
import labialAnterior from "../../../public/svgs/labial-anterior.9cf4e2c6..svg";
import occlussalLower from "../../../public/svgs/occlussal-lower.3be1bcdf..svg";
import occlussalUpper from "../../../public/svgs/occlussal-upper.cd664940..svg";
import profileRepose from "../../../public/svgs/profile-repose.cf7b4b65..svg";
import logo from "../../../public/images/logo.png";
import { toast } from 'react-toastify';

interface RetainerRequestModalProps {
    onClose: () => void;
}

const fileSchema = z.instanceof(File).nullable();

// Define schema with added scan validation
const retainerSchema = z.object({
    sets: z.number().min(1, 'At least 1 set is required'),
    archRequired: z.array(z.enum(['upper', 'lower'])).min(1, 'Select at least one arch'),
    notes: z.string().optional(),
    scanOption: z.enum(['upload_new', 'use_last']),
    photos: z.array(z.object({
        name: z.string(),
        file: fileSchema
    })).optional(),
    upperSTL: fileSchema,
    lowerSTL: fileSchema,
}).refine(data => {
    // If uploading new scans, both STLs must be provided
    if (data.scanOption === 'upload_new') {
        return data.upperSTL !== null && data.lowerSTL !== null;
    }
    return true;
}, {
    message: "Both upper and lower STL files are required when uploading new scans",
    path: ["upperSTL"],
});

type RetainerFormValues = z.infer<typeof retainerSchema>;

const RetainerRequestModal: React.FC<RetainerRequestModalProps> = ({ onClose }) => {
    const [currentStep, setCurrentStep] = useState<1 | 2 | 3>(1);
    const [photoUrls, setPhotoUrls] = useState<Record<string, string>>({});

    // Photo placeholders mapping
    const photoPlaceholders: Record<string, StaticImageData> = {
        profileRepose: profileRepose,
        frontalRepose: frontalRepose,
        frontalSmiling: frontalSmiling,
        occlussalUpper: occlussalUpper,
        socialSmile: logo,
        occlussalLower: occlussalLower,
        buccalRight: buccalRight,
        labialAnterior: labialAnterior,
        buccalLeft: buccalLeft,
    };

    // Initialize photos state
    const [photos, setPhotos] = useState<DentalPhoto[]>([
        { name: "profileRepose", file: null },
        { name: "frontalRepose", file: null },
        { name: "frontalSmiling", file: null },
        { name: "occlussalUpper", file: null },
        { name: "socialSmile", file: null },
        { name: "occlussalLower", file: null },
        { name: "buccalRight", file: null },
        { name: "labialAnterior", file: null },
        { name: "buccalLeft", file: null },
    ]);

    const {
        register,
        handleSubmit,
        watch,
        setValue,
        formState: { errors },
        trigger,
    } = useForm<RetainerFormValues>({
        resolver: zodResolver(retainerSchema),
        defaultValues: {
            sets: 1,
            archRequired: [],
            notes: '',
            scanOption: 'upload_new',
            photos: photos,
            upperSTL: null,
            lowerSTL: null,
        },
        mode: 'onChange'
    });

    const sets = watch('sets');
    const scanOption = watch('scanOption');
    const upperSTL = watch('upperSTL');
    const lowerSTL = watch('lowerSTL');
    const archRequired = watch('archRequired');
    const notes = watch('notes');

    // Create object URLs for photos when files are added/removed
    useEffect(() => {
        const urls: Record<string, string> = {};
        photos.forEach(photo => {
            if (photo.file) {
                urls[photo.name] = URL.createObjectURL(photo.file);
            }
        });
        setPhotoUrls(urls);

        // Cleanup function to revoke object URLs
        return () => {
            Object.values(urls).forEach(url => URL.revokeObjectURL(url));
        };
    }, [photos]);

    // Handle photo upload
    const handlePhotoFileChange = (e: React.ChangeEvent<HTMLInputElement>, photoName: string) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            // Update photos state
            const updatedPhotos = photos.map(photo =>
                photo.name === photoName ? { ...photo, file } : photo
            );
            setPhotos(updatedPhotos);
            // Update form value
            setValue('photos', updatedPhotos);
        }
    };

    // Handle photo deletion
    const handleDeletePhoto = (photoName: string) => {
        const updatedPhotos = photos.map(photo =>
            photo.name === photoName ? { ...photo, file: null } : photo
        );
        setPhotos(updatedPhotos);
        setValue('photos', updatedPhotos);
    };

    // Handle STL file changes
    const handleSTLFileChange = (e: React.ChangeEvent<HTMLInputElement>, field: 'upperSTL' | 'lowerSTL') => {
        if (e.target.files && e.target.files[0]) {
            setValue(field, e.target.files[0]);
        }
    };

    // Get image source for a photo
    const getImage = (photo: DentalPhoto): string | StaticImageData => {
        if (photo.file) {
            return photoUrls[photo.name] || photoPlaceholders[photo.name];
        }
        return photoPlaceholders[photo.name];
    };

    // Format photo name for display
    const formatPhotoName = (name: string): string => {
        return name
            .replace(/([A-Z])/g, ' $1') // Add space before capital letters
            .replace(/^./, str => str.toUpperCase()); // Capitalize first letter
    };

    // Handle next step with validation
    const handleNextStep = async (e: React.MouseEvent) => {
        e.preventDefault(); // Prevent any default behavior

        if (currentStep === 1) {
            // Check if archRequired has at least one selection
            if (!archRequired || archRequired.length === 0) {
                await trigger('archRequired'); // Trigger validation to show error
                return;
            }
            // Advance to next step
            setCurrentStep(2);
        }
        else if (currentStep === 2) {
            // Validate STL files if upload_new is selected
            if (scanOption === 'upload_new') {
                const isValid = await trigger(['upperSTL', 'lowerSTL']);
                if (!isValid) return;
            }
            // Advance to summary step
            setCurrentStep(3);
        }
    };

    const handleBack = (e: React.MouseEvent) => {
        e.preventDefault();
        if (currentStep === 2) {
            setCurrentStep(1);
        } else if (currentStep === 3) {
            setCurrentStep(2);
        }
    };

    const onSubmitForm = (data: RetainerFormValues) => {
        // Add photos to submitted data
        const submissionData = {
            ...data,
            photos
        };
        console.log('Retainer request data:', submissionData);

        // Store form data (stringify photos with file URLs instead of File objects)
        const storageData = {
            ...submissionData,
            photos: submissionData.photos?.map(photo => ({
                name: photo.name,
                fileUrl: photo.file ? URL.createObjectURL(photo.file) : null
            })),
            upperSTLName: data.upperSTL?.name || null,
            lowerSTLName: data.lowerSTL?.name || null,
        };
        localStorage.setItem('retainerRequestData', JSON.stringify(storageData));
        toast.success('Retainer request submitted successfully!');
        onClose();
    };

    // Prevent form submission when pressing Enter
    const preventEnterSubmit = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter' && currentStep !== 3) {
            e.preventDefault();
        }
    };

    return (
        <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4" onClick={e => e.stopPropagation()}>
            <div className="bg-white rounded-2xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto" onClick={e => e.stopPropagation()}>
                <div className="flex justify-between items-center p-6 border-b border-gray-200">
                    <h2 className="text-2xl font-bold text-gray-800 flex items-center">
                        Retainer Request
                        {currentStep === 2 && '- Scans'}
                        {currentStep === 3 && '- Summary'}
                    </h2>
                    <button
                        type="button"
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700 focus:outline-none"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                {/* Step indicator */}
                <div className="px-6 pt-6">
                    <div className="flex items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 1 ? 'bg-primary text-white' : 'bg-gray-200'}`}>
                            1
                        </div>
                        <div className={`h-1 flex-1 ${currentStep >= 2 ? 'bg-primary' : 'bg-gray-200'}`}></div>
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 2 ? 'bg-primary text-white' : 'bg-gray-200'}`}>
                            2
                        </div>
                        <div className={`h-1 flex-1 ${currentStep >= 3 ? 'bg-primary' : 'bg-gray-200'}`}></div>
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep >= 3 ? 'bg-primary text-white' : 'bg-gray-200'}`}>
                            3
                        </div>
                    </div>
                </div>

                <form onSubmit={handleSubmit(onSubmitForm)} onKeyDown={preventEnterSubmit}>
                    {currentStep === 1 ? (
                        <div className="p-6 space-y-6">
                            {/* Number of sets */}
                            <div>
                                <label className="block text-lg font-medium text-gray-800 mb-3">
                                    Number of sets<span className="text-red-500">*</span>
                                </label>
                                <div className="flex items-center">
                                    <button
                                        type="button"
                                        className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center"
                                        onClick={() => setValue('sets', Math.max(1, sets - 1))}
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
                                            <line x1="5" y1="12" x2="19" y2="12"></line>
                                        </svg>
                                    </button>
                                    <span className="mx-4 text-lg">{sets} sets</span>
                                    <button
                                        type="button"
                                        className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center"
                                        onClick={() => setValue('sets', sets + 1)}
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round">
                                            <line x1="12" y1="5" x2="12" y2="19"></line>
                                            <line x1="5" y1="12" x2="19" y2="12"></line>
                                        </svg>
                                    </button>
                                    <span className="ml-4 text-sm text-gray-500">(Free retainer included, additional retainers require extra fees)</span>
                                </div>
                                {errors.sets && <p className="mt-2 text-sm text-red-500">{errors.sets.message}</p>}
                            </div>

                            <div className="w-full border-t border-gray-200 my-4"></div>

                            {/* Arch Required */}
                            <div>
                                <label className="block text-lg font-medium text-gray-800 mb-3">
                                    Arch Required<span className="text-red-500">*</span>
                                </label>
                                <div className="flex gap-8">
                                    <label className="flex items-center">
                                        <input
                                            type="checkbox"
                                            className="form-checkbox h-5 w-5 text-primary rounded border-gray-300"
                                            value="upper"
                                            {...register('archRequired')}
                                        />
                                        <span className="ml-2 text-gray-700">Upper</span>
                                    </label>
                                    <label className="flex items-center">
                                        <input
                                            type="checkbox"
                                            className="form-checkbox h-5 w-5 text-primary rounded border-gray-300"
                                            value="lower"
                                            {...register('archRequired')}
                                        />
                                        <span className="ml-2 text-gray-700">Lower</span>
                                    </label>
                                </div>
                                {errors.archRequired && <p className="mt-2 text-sm text-red-500">{errors.archRequired.message}</p>}
                            </div>

                            <div className="w-full border-t border-gray-200 my-4"></div>

                            {/* Additional notes */}
                            <div>
                                <label className="block text-lg font-medium text-gray-800 mb-3">
                                    Additional notes:
                                </label>
                                <textarea
                                    className="w-full border border-gray-300 rounded-lg p-3 h-24"
                                    placeholder="Type here..."
                                    {...register('notes')}
                                ></textarea>
                            </div>
                        </div>
                    ) : currentStep === 2 ? (
                        <div className="p-6 space-y-6">
                            {/* Scans Section */}
                            <div>
                                <h3 className="text-lg font-medium text-gray-800 mb-3">Scans</h3>
                                <div className="flex gap-5 items-center mb-4">
                                    <div className="flex items-center">
                                        <input
                                            type="radio"
                                            id="upload_new"
                                            value="upload_new"
                                            {...register('scanOption')}
                                            className="form-radio h-5 w-5 text-primary border-gray-300"
                                        />
                                        <label htmlFor="upload_new" className="ml-2 text-gray-700 font-medium">Upload new scans</label>
                                    </div>

                                    <div className="flex items-center">
                                        <input
                                            type="radio"
                                            id="use_last"
                                            value="use_last"
                                            {...register('scanOption')}
                                            className="form-radio h-5 w-5 text-primary border-gray-300"
                                        />
                                        <label htmlFor="use_last" className="ml-2 text-gray-700 font-medium">Use last step</label>
                                    </div>
                                </div>

                                {scanOption === 'upload_new' && (
                                    <div className="bg-gray-50 p-5 rounded-lg">
                                        {/* STL Files Section - MANDATORY */}
                                        <div className="mb-6">
                                            <h4 className="text-md font-medium text-gray-800 mb-4">
                                                STL Files <span className="text-red-500">*</span>
                                            </h4>

                                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                                {/* Upper STL */}
                                                <div className="bg-white p-4 rounded-lg border border-gray-200">
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                                        Upper STL <span className="text-red-500">*</span>
                                                    </label>
                                                    <div className="flex items-center space-x-2">
                                                        <input
                                                            type="file"
                                                            accept=".zip"
                                                            onChange={(e) => handleSTLFileChange(e, 'upperSTL')}
                                                            className="block w-full text-sm text-gray-500
                                                                file:mr-4 file:py-2 file:px-4
                                                                file:rounded-full file:border-0
                                                                file:text-sm file:font-semibold
                                                                file:bg-primary file:text-white
                                                                hover:file:bg-primary-dark"
                                                        />
                                                    </div>
                                                    {upperSTL && (
                                                        <p className="mt-2 text-xs text-green-600">
                                                            File selected: {upperSTL.name}
                                                        </p>
                                                    )}
                                                </div>

                                                {/* Lower STL */}
                                                <div className="bg-white p-4 rounded-lg border border-gray-200">
                                                    <label className="block text-sm font-medium text-gray-700 mb-2">
                                                        Lower STL <span className="text-red-500">*</span>
                                                    </label>
                                                    <div className="flex items-center space-x-2">
                                                        <input
                                                            type="file"
                                                            accept=".zip"
                                                            onChange={(e) => handleSTLFileChange(e, 'lowerSTL')}
                                                            className="block w-full text-sm text-gray-500
                                                                file:mr-4 file:py-2 file:px-4
                                                                file:rounded-full file:border-0
                                                                file:text-sm file:font-semibold
                                                                file:bg-primary file:text-white
                                                                hover:file:bg-primary-dark"
                                                        />
                                                    </div>
                                                    {lowerSTL && (
                                                        <p className="mt-2 text-xs text-green-600">
                                                            File selected: {lowerSTL.name}
                                                        </p>
                                                    )}
                                                </div>
                                            </div>

                                            {/* Error messages for STL files */}
                                            {(errors.upperSTL || errors.lowerSTL) && (
                                                <p className="mt-2 text-sm text-red-500 bg-red-50 p-2 rounded">
                                                    {errors.upperSTL?.message || errors.lowerSTL?.message}
                                                </p>
                                            )}
                                        </div>

                                        {/* Photos Section - OPTIONAL */}
                                        <div className="mt-8">
                                            <h4 className="text-md font-medium text-gray-800 mb-2 flex items-center">
                                                Photos <span className="ml-2 text-sm font-normal text-gray-500">(Optional)</span>
                                            </h4>
                                            <p className="text-sm text-gray-600 mb-4">
                                                Upload patient photos if needed. These help with treatment planning but are not required.
                                            </p>

                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                                {photos.map((photo, index) =>
                                                    photo.name === "socialSmile" ? (
                                                        <div key={index} className="bg-white rounded-[10px] flex flex-col items-center justify-center p-5 border border-gray-200">
                                                            <Image
                                                                width={150}
                                                                height={150}
                                                                src={logo}
                                                                alt="Logo"
                                                                className="w-[150px] h-[150px] object-contain"
                                                            />
                                                            <p className="mt-2 text-sm text-gray-600">Social Smile</p>
                                                        </div>
                                                    ) : (
                                                        <div key={index} className="group relative">
                                                            <label
                                                                htmlFor={`upload-${photo.name}`}
                                                                className={`bg-white relative cursor-pointer rounded-[10px] border border-gray-200 ${photo.file ? "p-0" : "p-4"} flex flex-col items-center justify-center h-[180px]`}
                                                            >
                                                                <Image
                                                                    width={photo.file ? 180 : 120}
                                                                    height={photo.file ? 180 : 120}
                                                                    src={getImage(photo)}
                                                                    alt={photo.name}
                                                                    className={photo.file ? "w-full h-full object-cover rounded-[10px]" : "w-[120px] h-[120px] object-contain"}
                                                                />
                                                                {photo.file ? (
                                                                    <button
                                                                        onClick={(e) => {
                                                                            e.preventDefault();
                                                                            e.stopPropagation();
                                                                            handleDeletePhoto(photo.name);
                                                                        }}
                                                                        type="button"
                                                                        className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                                                                    >
                                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                                                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                                                        </svg>
                                                                    </button>
                                                                ) : (
                                                                    <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 bg-black/30 rounded-[10px] transition-opacity">
                                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                                                                        </svg>
                                                                    </div>
                                                                )}
                                                                <input
                                                                    id={`upload-${photo.name}`}
                                                                    type="file"
                                                                    accept="image/*"
                                                                    onChange={(e) => handlePhotoFileChange(e, photo.name)}
                                                                    className="hidden"
                                                                />
                                                            </label>
                                                            <p className="mt-1 text-xs text-gray-500 text-center">
                                                                {photo.name.replace(/([A-Z])/g, ' $1').trim()}
                                                            </p>
                                                        </div>
                                                    )
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {scanOption === 'use_last' && (
                                    <div className="bg-gray-50 p-5 rounded-lg">
                                        <p className="text-md font-medium text-gray-800 mb-2">
                                            Using your last step data
                                        </p>
                                        <p className="text-sm text-gray-600">
                                            Your previous data will be used to create your retainers. No need to upload new scans.
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                    ) : (
                        // STEP 3: SUMMARY
                        <div className="p-6 space-y-6">
                            <div className="bg-white p-5 rounded-lg border border-gray-200">
                                <h3 className="text-lg font-semibold text-gray-800 mb-4">Request Summary</h3>

                                {/* Basic Details */}
                                <div className="mb-6">
                                    <h4 className="text-md font-medium text-gray-700 mb-3 border-b pb-2">Basic Details</h4>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <p className="text-sm text-gray-500">Number of Sets</p>
                                            <p className="font-medium">{sets} {sets === 1 ? 'set' : 'sets'}</p>
                                        </div>
                                        <div>
                                            <p className="text-sm text-gray-500">Arch Required</p>
                                            <p className="font-medium">
                                                {archRequired.includes('upper') && archRequired.includes('lower')
                                                    ? 'Upper and Lower'
                                                    : archRequired.includes('upper')
                                                        ? 'Upper'
                                                        : 'Lower'}
                                            </p>
                                        </div>
                                    </div>

                                    {notes && (
                                        <div className="mt-4">
                                            <p className="text-sm text-gray-500">Additional Notes</p>
                                            <p className="mt-1 text-sm bg-gray-50 p-2 rounded">{notes}</p>
                                        </div>
                                    )}
                                </div>

                                {/* Scan Information */}
                                <div className="mb-6">
                                    <h4 className="text-md font-medium text-gray-700 mb-3 border-b pb-2">Scan Information</h4>
                                    <div>
                                        <p className="text-sm text-gray-500">Scan Option</p>
                                        <p className="font-medium">
                                            {scanOption === 'upload_new' ? 'Upload new scans' : 'Use last step data'}
                                        </p>
                                    </div>

                                    {scanOption === 'upload_new' && (
                                        <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                                            {upperSTL && (
                                                <div>
                                                    <p className="text-sm text-gray-500">Upper STL</p>
                                                    <p className="font-medium text-sm text-green-600">{upperSTL.name}</p>
                                                </div>
                                            )}

                                            {lowerSTL && (
                                                <div>
                                                    <p className="text-sm text-gray-500">Lower STL</p>
                                                    <p className="font-medium text-sm text-green-600">{lowerSTL.name}</p>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </div>

                                {/* Photos */}
                                {photos.some(photo => photo.file !== null) && (
                                    <div>
                                        <h4 className="text-md font-medium text-gray-700 mb-3 border-b pb-2">Photos</h4>
                                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                                            {photos
                                                .filter(photo => photo.file !== null && photo.name !== "socialSmile")
                                                .map((photo, idx) => (
                                                    <div key={idx} className="text-center">

                                                        <div className="bg-gray-100 rounded-md overflow-hidden h-[120px] w-full">
                                                            <Image
                                                                width={1000}
                                                                height={1000}
                                                                src={photoUrls[photo.name]}
                                                                alt={formatPhotoName(photo.name)}
                                                                className="w-full h-full object-cover"
                                                            />
                                                        </div>
                                                    </div>
                                                ))
                                            }
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}

                    <div className="border-t border-gray-200 p-6 flex justify-between">
                        {currentStep === 1 ? (
                            <div />
                        ) : (
                            <button
                                type="button"
                                onClick={handleBack}
                                className="px-6 py-2 border border-gray-300 rounded-full text-gray-700 font-medium hover:bg-gray-50 cursor-pointer"
                            >
                                Back
                            </button>
                        )}

                        <div className="flex gap-4">
                            <button
                                type="button"
                                onClick={onClose}
                                className="px-6 py-2 border border-gray-300 rounded-full text-gray-700 font-medium hover:bg-gray-50 cursor-pointer"
                            >
                                Cancel
                            </button>

                            {currentStep < 3 ? (
                                <button
                                    type="button"
                                    onClick={handleNextStep}
                                    className="px-6 py-2 bg-primary text-white rounded-full font-medium hover:bg-primary-dark cursor-pointer"
                                >
                                    Next
                                </button>
                            ) : (
                                <button
                                    type="submit"
                                    className="px-6 py-2 bg-primary text-white rounded-full font-medium cursor-pointer hover:bg-primary-dark"
                                >
                                    Submit Request
                                </button>
                            )}
                        </div>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default RetainerRequestModal;
