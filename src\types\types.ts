

export interface Refinement {
    id: number;
    description: string;
    created_at: string;
    updated_at: string;
}

export interface AlignerReplacement {
    id: number;
    aligner_id: number;
    reason: string;
    created_at: string;
    updated_at: string;
}

export interface Retainer {
    id: number;
    type: string;
    created_at: string;
    updated_at: string;
}

export interface RefinementAligner {
    id: number;
    aligner_id: number;
    refinement_id: number;
    created_at: string;
    updated_at: string;
}

export interface PatientFile {
    id: number;
    file_url: string;
    file_type: string;
    created_at: string;
    updated_at: string;
}

export interface PatientFileData {
    id: number;
    doctor_id: number;
    first_name: string;
    last_name: string;
    email: string;
    dob: string;
    gender: string;
    ship_to_office: string;
    bill_to_office: string;
    plan_id: number;
    clinical_conditions: string | null;
    general_notes: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    stlFile1: string | null;
    stlFile2: string | null;
    cbctFile: string | null;
    profileRepose: string | null;
    buccalRight: string | null;
    buccalLeft: string | null;
    frontalRepose: string | null;
    frontalSmiling: string | null;
    labialAnterior: string | null;
    occlussalLower: string | null;
    occlussalUpper: string | null;
    radioGraph1: string | null;
    radioGraph2: string | null;
    data: {
        chief_complaint: string;
        treatment_goals: string;
        notes: string;
    };
    country: string;
    plan: {
        id: number;
        name: string;
        created_at: string;
        updated_at: string;
        type: string;
        duration_years: number | null;
        expiration_date: string | null;
    };
    shipToOffice: {
        id: number;
        doctor_id: number;
        clinic_name: string;
        street_address: string;
        city: string;
        postal_code: string;
        phone_number: string;
        created_at: string;
        updated_at: string;
    };
    billToOffice: {
        id: number;
        doctor_id: number;
        clinic_name: string;
        street_address: string;
        city: string;
        postal_code: string;
        phone_number: string;
        created_at: string;
        updated_at: string;
    };
    refinements: Refinement[];
    alignerReplacements: AlignerReplacement[];
    retainers: Retainer[];
    refinementsAligner: RefinementAligner[];
    patientFiles: PatientFile[];
    status: string;
    uuid: string | null
}

export interface PatientData {
    id: number;
    doctor_id: number;
    first_name: string;
    last_name: string;
    email: string;
    dob: string;
    gender: string;
    ship_to_office: string;
    bill_to_office: string;
    plan_id: number;
    clinical_conditions: string | null;
    general_notes: string | null;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    stlFile1: string | null;
    stlFile2: string | null;
    cbctFile: string | null;
    profileRepose: string | null;
    buccalRight: string | null;
    buccalLeft: string | null;
    frontalRepose: string | null;
    frontalSmiling: string | null;
    labialAnterior: string | null;
    occlussalLower: string | null;
    occlussalUpper: string | null;
    radioGraph1: string | null;
    radioGraph2: string | null;
    data: string | null;
    country: string;
    plan_name: string | null;
    status: string | null;
    uuid:  string | null;
}

export interface Address {
  id: number;
  clinic_name: string;
  street_address: string;
  city: string;
  postal_code: string;
  phone_number: string;
  address_type: string;
}

export interface Plan {
  id: number;
  name: string;
  type: string;
  duration_years: number | null;
  expiration_date: string | null;
  created_at: string;
  updated_at: string;
}
export interface PlansResponse {
  plans: Plan[]; // Array of plans
  message: string; // Success message
}


export interface CreatePatientResponse {
  status: number;
  success: boolean;
  data: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    dob: string;
    gender: string;
    country: string;
    ship_to_office: string;
    bill_to_office: string;
    created_at: string;
    updated_at: string;
  };
  message: string;
}