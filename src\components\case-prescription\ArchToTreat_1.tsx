import { FieldErrors, FieldValues, Path, PathValue, UseFormRegister, UseFormSetValue, UseFormWatch } from "react-hook-form";
import RoundRadioButton from "../reuseable/RoundRadioButton";
// import PatientTypeHeading from "../lite-package/PatientTypeHeading";
import { useEffect } from "react";
import { AnimatePresence, motion } from "framer-motion";

export interface CommenProps<T extends FieldValues> {
    register: UseFormRegister<T>
    watch: UseFormWatch<T>
    errors: FieldErrors<T>
    setValue: UseFormSetValue<T>
    number: string
}

export const DefaultTransition = { duration: 0.3, ease: 'easeInOut' }

const ArchToTreat_1 = <T extends FieldValues,>({ register, errors, watch, setValue, number }: CommenProps<T>) => {
    const selectedArch = watch("archToTreat.option" as Path<T>)

    useEffect(() => {
        setValue("archToTreat.value" as Path<T>, "" as PathValue<T, Path<T>>)
    }, [selectedArch, setValue])
    return (
        <div className="transition-all duration-300 ease-in-out h-auto">
            <div>
                <div className="flex item-center justify-between mb-2">
                    <h3 className="font-bold text-lg  text-dark">{`${number}`}{" "}Arch to Treat</h3>
                    {/* <div className="flex justify-between items-center">
                        <PatientTypeHeading text='Patient Type: Adult' />
                    </div> */}
                </div>
                <div className="flex items-center gap-2 mb-2">
                    <RoundRadioButton
                        id="archToTreatBoth"
                        label="Both"
                        value="both"
                        register={register}
                        name="archToTreat.option"
                        labelClass='!text-[#434343] text-base'
                    />
                    <RoundRadioButton
                        id="archToTreatUpper"
                        label="Upper"
                        value="upper"
                        register={register}
                        name="archToTreat.option"
                        labelClass='!text-[#434343] text-base'

                    />

                    <RoundRadioButton
                        id="archToTreatLower"
                        label="Lower"
                        value="lower"
                        register={register}
                        name="archToTreat.option"
                        labelClass='!text-[#434343] text-base'
                    />
                </div>
                {errors.archToTreat && "option" in errors.archToTreat && errors.archToTreat.option && "message" in errors.archToTreat.option && (
                    <p className="text-red-500 text-sm mt-1">{String(errors.archToTreat.option.message)}</p>
                )}
            </div>

            <AnimatePresence initial={false} mode="wait">
                {selectedArch === 'upper' && (
                    <motion.div
                        key="upper"
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={DefaultTransition}
                        style={{ overflow: 'hidden' }}
                    >
                        <div className="px-6 flex gap-2 items-center">
                            <RoundRadioButton
                                id="upperArch1"
                                label="Passive aligners on opposite arch"
                                value="Passive aligners on opposite arch"
                                name="archToTreat.value"
                                register={register}
                                labelClass="!text-[#434343] text-base"
                            />
                            <RoundRadioButton
                                id="upperArch2"
                                label="No aligners on opposite arch"
                                value="No aligners on opposite arch"
                                name="archToTreat.value"
                                register={register}
                                labelClass="!text-[#434343] text-base"
                            />
                        </div>
                        {errors.archToTreat && "value" in errors.archToTreat && errors.archToTreat.value && "message" in errors.archToTreat.value && (
                            <p className="text-red-500 text-sm mt-1">
                                {String(errors.archToTreat.value.message)}
                            </p>
                        )}
                    </motion.div>
                )}

                {selectedArch === 'lower' && (
                    <motion.div
                        key="lower"
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: 'auto', opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={DefaultTransition}
                        style={{ overflow: 'hidden' }}
                    >
                        <div className="flex gap-2 items-center px-6">
                            <RoundRadioButton
                                id="lowerArch1"
                                label="Passive aligners on opposite arch"
                                value="Passive aligners on opposite arch"
                                name="archToTreat.value"
                                register={register}
                                labelClass="!text-[#434343] text-base"
                            />
                            <RoundRadioButton
                                id="lowerArch2"
                                label="No aligners on opposite arch"
                                value="No aligners on opposite arch"
                                name="archToTreat.value"
                                register={register}
                                labelClass="!text-[#434343] text-base"
                            />
                        </div>
                        {errors.archToTreat && "value" in errors.archToTreat && errors.archToTreat.value && "message" in errors.archToTreat.value && (
                            <p className="text-red-500 text-sm mt-1">
                                {String(errors.archToTreat.value.message)}
                            </p>
                        )}
                    </motion.div>
                )}
            </AnimatePresence>
        </div>
    )
}

export default ArchToTreat_1